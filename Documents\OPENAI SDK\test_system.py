"""
Test script for the Multi-Agent System
"""
import asyncio
import sys
import os

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from main import MultiAgentSystem

async def test_system():
    """Test the multi-agent system with sample queries"""
    print("🧪 Testing Multi-Agent System")
    print("=" * 40)
    
    # Initialize system
    system = MultiAgentSystem()
    
    try:
        await system.initialize()
        print("✅ System initialization successful")
        
        # Test queries
        test_queries = [
            {
                "query": "What is machine learning?",
                "type": "ai_research",
                "description": "AI Research Agent Test"
            },
            {
                "query": "What are the symptoms of diabetes?",
                "type": "healthcare", 
                "description": "Healthcare Agent Test"
            },
            {
                "query": "AI applications in medical diagnosis",
                "type": "parallel",
                "description": "Parallel Processing Test"
            }
        ]
        
        for i, test in enumerate(test_queries, 1):
            print(f"\n🔬 Test {i}: {test['description']}")
            print(f"Query: {test['query']}")
            print(f"Type: {test['type']}")
            
            try:
                result = await system.process_query(test['query'], test['type'])
                
                if 'error' in result:
                    print(f"❌ Test {i} failed: {result['error']}")
                else:
                    print(f"✅ Test {i} passed")
                    # Print a brief summary of the result
                    if isinstance(result.get('result'), dict):
                        if 'response' in result['result']:
                            response = result['result']['response']
                            print(f"Response preview: {response[:100]}...")
                        elif 'synthesis' in result['result']:
                            print("✅ Synthesis completed successfully")
                        else:
                            print("✅ Structured result returned")
                    else:
                        print(f"Response preview: {str(result.get('result', ''))[:100]}...")
                        
            except Exception as e:
                print(f"❌ Test {i} failed with exception: {str(e)}")
        
        # Test system status
        print(f"\n📊 Final System Status:")
        await system.display_system_status()
        
        print("\n🎉 All tests completed!")
        
    except Exception as e:
        print(f"❌ System test failed: {str(e)}")
        import traceback
        traceback.print_exc()

async def test_configuration():
    """Test system configuration"""
    print("🔧 Testing Configuration")
    print("-" * 25)
    
    try:
        from config import Config
        
        # Test configuration validation
        Config.validate_config()
        print("✅ Configuration validation passed")
        
        # Test agent configurations
        for agent_type in ['healthcare', 'ai_research', 'coordinator']:
            config = Config.get_agent_config(agent_type)
            if config:
                print(f"✅ {agent_type.title()} agent config loaded")
            else:
                print(f"⚠️  {agent_type.title()} agent config empty")
        
    except Exception as e:
        print(f"❌ Configuration test failed: {str(e)}")

async def test_imports():
    """Test all module imports"""
    print("📦 Testing Module Imports")
    print("-" * 25)
    
    modules_to_test = [
        ('config', 'Config'),
        ('utils.gemini_client', 'gemini_client'),
        ('tools.memory_tools', 'AgentMemoryManager'),
        ('tools.research_tools', 'web_search'),
        ('agents.base_agent', 'BaseAgent'),
        ('agents.healthcare_agent', 'HealthcareAgent'),
        ('agents.ai_research_agent', 'AIResearchAgent'),
        ('agents.coordinator_agent', 'CoordinatorAgent')
    ]
    
    for module_name, class_name in modules_to_test:
        try:
            module = __import__(module_name, fromlist=[class_name])
            getattr(module, class_name)
            print(f"✅ {module_name}.{class_name}")
        except Exception as e:
            print(f"❌ {module_name}.{class_name}: {str(e)}")

async def main():
    """Main test function"""
    print("🤖 Multi-Agent System Test Suite")
    print("=" * 50)
    
    # Test imports first
    await test_imports()
    print()
    
    # Test configuration
    await test_configuration()
    print()
    
    # Test system functionality
    await test_system()

if __name__ == "__main__":
    asyncio.run(main())
