"""
Main execution script for the Multi-Agent Communication System
"""
import asyncio
import json
import sys
import os
from datetime import datetime
from typing import Dict, Any, Optional

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from agents.coordinator_agent import CoordinatorAgent
from agents.healthcare_agent import HealthcareAgent
from agents.ai_research_agent import AIResearchAgent
from tools.communication_tools import communication_hub
from config import Config

# Import memory initialization based on configuration
if Config.MEMORY_CONFIG.get("use_mem0", True):
    from tools.mem0_mcp_tools import initialize_mem0_connection, cleanup_mem0_connection
    USING_MEM0 = True
else:
    USING_MEM0 = False

class MultiAgentSystem:
    """Main class for the multi-agent communication system"""

    def __init__(self):
        self.coordinator = None
        self.healthcare_agent = None
        self.ai_research_agent = None
        self.system_initialized = False
        self.session_id = f"session_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

    async def initialize(self):
        """Initialize the multi-agent communication system"""
        try:
            print("🚀 Initializing Multi-Agent Communication System...")
            print(f"📋 Session ID: {self.session_id}")

            # Validate configuration
            Config.validate_config()
            print("✅ Configuration validated")

            # Initialize memory system
            if USING_MEM0:
                print("🔗 Initializing Mem0 MCP connection...")
                await initialize_mem0_connection()
                print("✅ Mem0 MCP memory system initialized")
            else:
                print("📁 Using local file-based memory system")

            # Initialize all agents (they auto-register with communication hub)
            print("🤖 Initializing agents...")

            self.healthcare_agent = HealthcareAgent()
            print("✅ Healthcare Research Agent initialized")

            self.ai_research_agent = AIResearchAgent()
            print("✅ AI Research Agent initialized")

            self.coordinator = CoordinatorAgent()
            print("✅ Coordinator Agent initialized")

            # Wait a moment for all registrations to complete
            await asyncio.sleep(0.5)

            self.system_initialized = True
            print("🎉 Multi-Agent Communication System initialized successfully!")

            # Display system status
            await self.display_system_status()

            # Display communication capabilities
            await self.display_communication_status()

            # Display memory system status
            await self.display_memory_status()

        except Exception as e:
            print(f"❌ Initialization failed: {str(e)}")
            raise
    
    async def display_system_status(self):
        """Display current system status"""
        if not self.system_initialized:
            print("❌ System not initialized")
            return

        print("\n📊 System Status:")
        print(f"   Healthcare Agent: {self.healthcare_agent.name} - {'Active' if self.healthcare_agent.is_active else 'Ready'}")
        print(f"   AI Research Agent: {self.ai_research_agent.name} - {'Active' if self.ai_research_agent.is_active else 'Ready'}")
        print(f"   Coordinator Agent: {self.coordinator.name} - {'Active' if self.coordinator.is_active else 'Ready'}")
        print()

    async def display_communication_status(self):
        """Display communication system status"""
        print("📡 Communication System Status:")

        # Get agent status from communication hub
        agent_status = communication_hub.get_agent_status()

        print(f"   Registered Agents: {len(agent_status)}")
        for agent_id, status in agent_status.items():
            capabilities = ', '.join(status['capabilities'][:3]) + ('...' if len(status['capabilities']) > 3 else '')
            print(f"   • {agent_id}: {status['pending_messages']} pending messages | Capabilities: {capabilities}")

        print(f"   Communication Features:")
        print(f"   • Inter-agent messaging: ✅ Enabled")
        print(f"   • Collaboration requests: ✅ Enabled")
        print(f"   • Broadcast messaging: ✅ Enabled")
        print(f"   • Max collaboration depth: {Config.MAX_COLLABORATION_DEPTH}")
        print()

    async def display_memory_status(self):
        """Display memory system status"""
        print("🧠 Memory System Status:")

        if USING_MEM0:
            print("   Memory Backend: Mem0 MCP Server")
            print(f"   Server URL: {Config.MEM0_CONFIG['server_url']}")
            print("   Features:")
            print("   • Advanced semantic search")
            print("   • Persistent memory storage")
            print("   • Cross-agent memory sharing")
            print("   • Real-time memory updates")

            # Test connection
            try:
                if Config.MEMORY_CONFIG.get("use_mem0", True):
                    from tools.mem0_mcp_tools import mem0_client
                    if mem0_client.connected:
                        print("   Connection Status: ✅ Connected")
                    else:
                        print("   Connection Status: ⚠️ Not connected (will connect on first use)")
            except Exception as e:
                print(f"   Connection Status: ❌ Error - {str(e)}")
        else:
            print("   Memory Backend: Local File Storage")
            print(f"   Storage Path: {Config.MEMORY_CONFIG['storage_path']}")
            print("   Features:")
            print("   • Local file-based storage")
            print("   • Basic text search")
            print("   • Agent-specific memory files")

        print()
    
    async def process_query(self, query: str, query_type: str = "auto") -> Dict[str, Any]:
        """Process a user query through the multi-agent communication system"""
        if not self.system_initialized:
            raise RuntimeError("System not initialized. Call initialize() first.")

        print(f"🔍 Processing query: {query[:100]}{'...' if len(query) > 100 else ''}")
        print(f"📝 Query type: {query_type}")

        try:
            # Determine query type if auto
            if query_type == "auto":
                query_type = self._classify_query(query)
                print(f"🤖 Auto-classified as: {query_type}")

            # Route query to appropriate agent(s) - they will communicate as needed
            if query_type == "healthcare":
                print("🏥 Routing to Healthcare Agent...")
                result = await self.healthcare_agent.process_message(query)
            elif query_type == "ai_research":
                print("🧠 Routing to AI Research Agent...")
                result = await self.ai_research_agent.process_message(query)
            elif query_type == "coordination" or query_type == "parallel":
                print("🎯 Routing to Coordinator Agent...")
                result = await self.coordinator.process_message(query)
            else:
                # Default to coordinator for complex queries
                print("🎯 Routing to Coordinator Agent (default)...")
                result = await self.coordinator.process_message(query)

            print("✅ Query processed successfully")

            # Check for any inter-agent communications that occurred
            await self._display_communication_summary()

            return {
                "query": query,
                "query_type": query_type,
                "result": result,
                "session_id": self.session_id,
                "timestamp": datetime.now().isoformat()
            }

        except Exception as e:
            error_result = {
                "query": query,
                "query_type": query_type,
                "error": str(e),
                "session_id": self.session_id,
                "timestamp": datetime.now().isoformat()
            }
            print(f"❌ Query processing failed: {str(e)}")
            return error_result
    
    def _classify_query(self, query: str) -> str:
        """Classify query to determine appropriate routing"""
        query_lower = query.lower()
        
        # Healthcare keywords
        healthcare_keywords = [
            'health', 'medical', 'disease', 'treatment', 'clinical', 'patient',
            'drug', 'medication', 'therapy', 'diagnosis', 'symptom', 'healthcare',
            'hospital', 'doctor', 'nurse', 'pharmaceutical', 'epidemiology'
        ]
        
        # AI research keywords
        ai_keywords = [
            'ai', 'artificial intelligence', 'machine learning', 'deep learning',
            'neural network', 'algorithm', 'model', 'nlp', 'computer vision',
            'transformer', 'gpt', 'llm', 'automation', 'robotics', 'data science'
        ]
        
        # Check for healthcare keywords
        healthcare_score = sum(1 for keyword in healthcare_keywords if keyword in query_lower)
        
        # Check for AI keywords
        ai_score = sum(1 for keyword in ai_keywords if keyword in query_lower)
        
        # Check for coordination keywords
        coordination_keywords = [
            'coordinate', 'manage', 'organize', 'plan', 'synthesize', 'combine',
            'compare', 'analyze together', 'multi-domain', 'interdisciplinary'
        ]
        coordination_score = sum(1 for keyword in coordination_keywords if keyword in query_lower)

        # Determine routing
        if coordination_score > 0 or (healthcare_score > 0 and ai_score > 0):
            return "coordination"
        elif healthcare_score > ai_score:
            return "healthcare"
        elif ai_score > healthcare_score:
            return "ai_research"
        else:
            return "coordination"  # Default to coordination for complex queries

    async def _display_communication_summary(self):
        """Display summary of inter-agent communications that occurred"""
        agent_status = communication_hub.get_agent_status()

        total_messages = sum(status['pending_messages'] for status in agent_status.values())
        if total_messages > 0:
            print(f"\n📨 Communication Activity:")
            for agent_id, status in agent_status.items():
                if status['pending_messages'] > 0:
                    print(f"   • {agent_id}: {status['pending_messages']} new messages")
        else:
            print(f"\n📨 No inter-agent communications during this query")
    
    def _split_query_for_parallel(self, query: str) -> tuple:
        """Split a query into healthcare and AI research components"""
        # Simple splitting strategy - can be enhanced
        healthcare_task = f"Analyze the healthcare aspects of: {query}"
        ai_task = f"Analyze the AI/technology aspects of: {query}"
        
        return healthcare_task, ai_task
    
    async def run_interactive_mode(self):
        """Run the system in interactive mode"""
        if not self.system_initialized:
            await self.initialize()
        
        print("\n🎯 Multi-Agent System - Interactive Mode")
        print("Type 'help' for commands, 'quit' to exit")
        print("-" * 50)
        
        while True:
            try:
                # Get user input
                user_input = input("\n💬 Enter your query: ").strip()
                
                if not user_input:
                    continue
                
                # Handle commands
                if user_input.lower() == 'quit':
                    print("👋 Goodbye!")
                    break
                elif user_input.lower() == 'help':
                    self._display_help()
                    continue
                elif user_input.lower() == 'status':
                    await self.display_system_status()
                    continue
                elif user_input.lower().startswith('type:'):
                    # Handle explicit type specification
                    parts = user_input.split(':', 1)
                    if len(parts) == 2:
                        query_type = parts[0].replace('type', '').strip()
                        query = parts[1].strip()
                        result = await self.process_query(query, query_type)
                    else:
                        print("❌ Invalid format. Use: type:healthcare:your query")
                        continue
                else:
                    # Process regular query
                    result = await self.process_query(user_input)
                
                # Display result
                self._display_result(result)
                
            except KeyboardInterrupt:
                print("\n👋 Goodbye!")
                break
            except Exception as e:
                print(f"❌ Error: {str(e)}")
    
    def _display_help(self):
        """Display help information"""
        help_text = """
🆘 Multi-Agent Communication System Help

Commands:
  help                    - Show this help message
  status                  - Display system and communication status
  quit                    - Exit the system

Query Types:
  [query]                 - Auto-classify and process query (agents communicate as needed)
  type:healthcare:[query] - Route to healthcare agent
  type:ai_research:[query]- Route to AI research agent
  type:coordination:[query] - Route to coordinator agent
  type:parallel:[query]   - Process with coordination (same as coordination)

Communication Features:
  • Agents automatically collaborate when they need expertise from other domains
  • Inter-agent messaging enables knowledge sharing during query processing
  • Broadcast messaging allows agents to share insights with all other agents
  • Collaboration requests let agents ask for help with specific tasks

Examples:
  What are the latest treatments for diabetes?
  type:ai_research:Latest developments in transformer models
  type:coordination:Compare AI and traditional approaches to medical diagnosis
  How can machine learning improve healthcare outcomes?
        """
        print(help_text)

    async def cleanup(self):
        """Cleanup system resources"""
        try:
            if USING_MEM0:
                print("🔗 Cleaning up Mem0 MCP connection...")
                await cleanup_mem0_connection()
                print("✅ Mem0 MCP connection cleaned up")

            print("🧹 System cleanup completed")
        except Exception as e:
            print(f"⚠️ Cleanup warning: {str(e)}")
    
    def _display_result(self, result: Dict[str, Any]):
        """Display query result in a formatted way"""
        print("\n📋 Result:")
        print("-" * 30)
        
        if "error" in result:
            print(f"❌ Error: {result['error']}")
            return
        
        # Display basic info
        print(f"Query Type: {result.get('query_type', 'Unknown')}")
        print(f"Timestamp: {result.get('timestamp', 'Unknown')}")
        
        # Display result content
        result_content = result.get('result', {})
        if isinstance(result_content, dict):
            if 'response' in result_content:
                print(f"\n📝 Response:\n{result_content['response']}")
            elif 'synthesis' in result_content:
                print(f"\n🔬 Synthesis:\n{result_content['synthesis']}")
            else:
                print(f"\n📊 Result:\n{json.dumps(result_content, indent=2)[:500]}...")
        else:
            print(f"\n📝 Response:\n{str(result_content)}")
        
        print("-" * 30)

async def main():
    """Main function"""
    print("🤖 Multi-Agent Communication System with Google Gemini")
    print("🔗 Featuring Inter-Agent Collaboration and Communication")
    print("=" * 65)
    
    # Create and initialize system
    system = MultiAgentSystem()
    
    try:
        # Initialize the system
        await system.initialize()

        # Check if command line arguments are provided
        if len(sys.argv) > 1:
            # Process command line query
            query = " ".join(sys.argv[1:])
            result = await system.process_query(query)
            system._display_result(result)
        else:
            # Run in interactive mode
            await system.run_interactive_mode()

    except KeyboardInterrupt:
        print("\n👋 System interrupted by user")
    except Exception as e:
        print(f"❌ System error: {str(e)}")
        import traceback
        traceback.print_exc()
    finally:
        # Cleanup resources
        try:
            await system.cleanup()
        except Exception as e:
            print(f"⚠️ Cleanup error: {str(e)}")

    print("\n👋 Thank you for using the Multi-Agent Communication System!")

if __name__ == "__main__":
    # Run the main function
    asyncio.run(main())
