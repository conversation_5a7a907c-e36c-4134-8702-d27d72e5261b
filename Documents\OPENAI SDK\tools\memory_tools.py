"""
Memory management tools using Mem0 for multi-agent systems
"""
import asyncio
from typing import Dict, List, Any, Optional
from mem0 import Memory
from config import Config
import json
import os

class AgentMemoryManager:
    """Memory manager for individual agents using Mem0"""
    
    def __init__(self, agent_id: str):
        self.agent_id = agent_id
        self.memory = Memory(Config.MEM0_CONFIG)
        self.session_id = f"agent_{agent_id}_session"
    
    async def store_memory(self, content: str, metadata: Dict[str, Any] = None) -> str:
        """Store a memory for the agent"""
        try:
            memory_data = {
                "content": content,
                "agent_id": self.agent_id,
                "metadata": metadata or {}
            }
            
            # Store memory
            result = await asyncio.get_event_loop().run_in_executor(
                None,
                lambda: self.memory.add(
                    messages=[{"role": "user", "content": content}],
                    user_id=self.agent_id,
                    metadata=memory_data["metadata"]
                )
            )
            
            return result.get("id", "unknown") if isinstance(result, dict) else str(result)
            
        except Exception as e:
            print(f"Error storing memory for agent {self.agent_id}: {str(e)}")
            return ""
    
    async def retrieve_memories(self, query: str, limit: int = 5) -> List[Dict[str, Any]]:
        """Retrieve relevant memories for the agent"""
        try:
            results = await asyncio.get_event_loop().run_in_executor(
                None,
                lambda: self.memory.search(
                    query=query,
                    user_id=self.agent_id,
                    limit=limit
                )
            )
            
            return results if isinstance(results, list) else []
            
        except Exception as e:
            print(f"Error retrieving memories for agent {self.agent_id}: {str(e)}")
            return []
    
    async def get_all_memories(self) -> List[Dict[str, Any]]:
        """Get all memories for the agent"""
        try:
            results = await asyncio.get_event_loop().run_in_executor(
                None,
                lambda: self.memory.get_all(user_id=self.agent_id)
            )
            
            return results if isinstance(results, list) else []
            
        except Exception as e:
            print(f"Error getting all memories for agent {self.agent_id}: {str(e)}")
            return []
    
    async def delete_memory(self, memory_id: str) -> bool:
        """Delete a specific memory"""
        try:
            await asyncio.get_event_loop().run_in_executor(
                None,
                lambda: self.memory.delete(memory_id=memory_id)
            )
            return True
            
        except Exception as e:
            print(f"Error deleting memory {memory_id} for agent {self.agent_id}: {str(e)}")
            return False

class SharedMemoryManager:
    """Shared memory manager for cross-agent communication"""
    
    def __init__(self):
        self.memory = Memory(Config.MEM0_CONFIG)
        self.shared_user_id = "shared_agent_memory"
    
    async def store_shared_memory(
        self, 
        content: str, 
        source_agent: str, 
        target_agents: List[str] = None,
        metadata: Dict[str, Any] = None
    ) -> str:
        """Store shared memory accessible by multiple agents"""
        try:
            memory_data = {
                "content": content,
                "source_agent": source_agent,
                "target_agents": target_agents or [],
                "metadata": metadata or {}
            }
            
            result = await asyncio.get_event_loop().run_in_executor(
                None,
                lambda: self.memory.add(
                    messages=[{"role": "assistant", "content": content}],
                    user_id=self.shared_user_id,
                    metadata=memory_data
                )
            )
            
            return result.get("id", "unknown") if isinstance(result, dict) else str(result)
            
        except Exception as e:
            print(f"Error storing shared memory: {str(e)}")
            return ""
    
    async def retrieve_shared_memories(
        self, 
        query: str, 
        requesting_agent: str,
        limit: int = 5
    ) -> List[Dict[str, Any]]:
        """Retrieve shared memories relevant to the query"""
        try:
            results = await asyncio.get_event_loop().run_in_executor(
                None,
                lambda: self.memory.search(
                    query=query,
                    user_id=self.shared_user_id,
                    limit=limit
                )
            )
            
            # Filter results based on target agents if specified
            filtered_results = []
            for result in (results if isinstance(results, list) else []):
                metadata = result.get("metadata", {})
                target_agents = metadata.get("target_agents", [])
                
                # Include if no target agents specified or requesting agent is in target list
                if not target_agents or requesting_agent in target_agents:
                    filtered_results.append(result)
            
            return filtered_results
            
        except Exception as e:
            print(f"Error retrieving shared memories: {str(e)}")
            return []

# Memory tool functions for agents
async def store_agent_memory(agent_id: str, content: str, metadata: Dict[str, Any] = None) -> str:
    """Tool function to store memory for an agent"""
    manager = AgentMemoryManager(agent_id)
    return await manager.store_memory(content, metadata)

async def retrieve_agent_memories(agent_id: str, query: str, limit: int = 5) -> List[Dict[str, Any]]:
    """Tool function to retrieve memories for an agent"""
    manager = AgentMemoryManager(agent_id)
    return await manager.retrieve_memories(query, limit)

async def store_shared_memory(
    content: str, 
    source_agent: str, 
    target_agents: List[str] = None,
    metadata: Dict[str, Any] = None
) -> str:
    """Tool function to store shared memory"""
    manager = SharedMemoryManager()
    return await manager.store_shared_memory(content, source_agent, target_agents, metadata)

async def retrieve_shared_memories(
    query: str, 
    requesting_agent: str,
    limit: int = 5
) -> List[Dict[str, Any]]:
    """Tool function to retrieve shared memories"""
    manager = SharedMemoryManager()
    return await manager.retrieve_shared_memories(query, requesting_agent, limit)
