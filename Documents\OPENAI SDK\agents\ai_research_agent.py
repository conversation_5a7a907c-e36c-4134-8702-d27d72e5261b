"""
AI Research Agent - Specialized in AI/ML research and technology analysis
"""
import asyncio
from typing import Dict, List, Any, Optional
import sys
import os
import re

# Add the parent directory to the path to import modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from agents.base_agent import BaseAgent
from tools.research_tools import extract_topics, summarize_research
from config import Config

class AIResearchAgent(BaseAgent):
    """AI Research Agent specializing in AI/ML research and technology analysis"""
    
    def __init__(self):
        # Load system prompt
        prompt_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), "prompts", "ai_research_prompt.md")
        with open(prompt_path, 'r', encoding='utf-8') as f:
            system_prompt = f.read()
        
        super().__init__(
            agent_id="ai_research_agent",
            name="AI Research Agent",
            description="Specializes in AI/ML research, technology trends, algorithm analysis, and technical innovation",
            system_prompt=system_prompt,
            tools=self._setup_ai_research_tools()
        )
    
    def _setup_ai_research_tools(self) -> List[Dict[str, Any]]:
        """Setup AI research-specific tools"""
        ai_tools = [
            {
                "name": "search_ai_papers",
                "description": "Search for AI/ML research papers and technical literature",
                "function": self._search_ai_papers,
                "parameters": {
                    "type": "object",
                    "properties": {
                        "query": {"type": "string", "description": "AI research query"},
                        "domain": {"type": "string", "description": "AI domain (e.g., NLP, computer vision, ML)"},
                        "venue": {"type": "string", "description": "Conference or journal (e.g., NeurIPS, ICML, ICLR)"},
                        "year": {"type": "string", "description": "Publication year or range"}
                    },
                    "required": ["query"]
                }
            },
            {
                "name": "analyze_ai_trends",
                "description": "Analyze AI technology trends and developments",
                "function": self._analyze_ai_trends,
                "parameters": {
                    "type": "object",
                    "properties": {
                        "technology": {"type": "string", "description": "AI technology or technique"},
                        "time_frame": {"type": "string", "description": "Time frame for trend analysis"},
                        "industry": {"type": "string", "description": "Industry or application domain"}
                    },
                    "required": ["technology"]
                }
            },
            {
                "name": "evaluate_ai_model",
                "description": "Evaluate AI model performance and capabilities",
                "function": self._evaluate_ai_model,
                "parameters": {
                    "type": "object",
                    "properties": {
                        "model_name": {"type": "string", "description": "Name of the AI model"},
                        "task": {"type": "string", "description": "Task or benchmark"},
                        "metrics": {"type": "string", "description": "Performance metrics to analyze"}
                    },
                    "required": ["model_name"]
                }
            },
            {
                "name": "analyze_ai_ethics",
                "description": "Analyze AI ethics, bias, and responsible AI considerations",
                "function": self._analyze_ai_ethics,
                "parameters": {
                    "type": "object",
                    "properties": {
                        "topic": {"type": "string", "description": "AI ethics topic or concern"},
                        "context": {"type": "string", "description": "Application context or scenario"},
                        "stakeholders": {"type": "string", "description": "Affected stakeholders"}
                    },
                    "required": ["topic"]
                }
            },
            {
                "name": "research_ai_applications",
                "description": "Research AI applications in specific industries or domains",
                "function": self._research_ai_applications,
                "parameters": {
                    "type": "object",
                    "properties": {
                        "industry": {"type": "string", "description": "Industry or domain"},
                        "use_case": {"type": "string", "description": "Specific use case or application"},
                        "maturity": {"type": "string", "description": "Technology maturity level"}
                    },
                    "required": ["industry"]
                }
            }
        ]
        return ai_tools
    
    async def _search_ai_papers(self, query: str, domain: str = None, venue: str = None, year: str = None) -> Dict[str, Any]:
        """Search for AI/ML research papers and technical literature"""
        try:
            # Enhance query with AI research terms
            enhanced_query = f"{query} artificial intelligence machine learning"
            if domain:
                enhanced_query += f" {domain}"
            if venue:
                enhanced_query += f" {venue}"
            if year:
                enhanced_query += f" {year}"
            
            # Add AI research sources
            enhanced_query += " site:arxiv.org OR site:papers.nips.cc OR site:openreview.net OR site:aclanthology.org"
            
            # Use web search tool
            from tools.research_tools import web_search
            results = await web_search(enhanced_query, num_results=8)
            
            # Filter and enhance results for AI relevance
            ai_results = []
            for result in results:
                if self._is_ai_research_source(result.get('url', '')):
                    ai_results.append({
                        **result,
                        'ai_relevance': 'high',
                        'source_type': self._classify_ai_source(result.get('url', '')),
                        'technical_level': self._assess_technical_level(result.get('content', ''))
                    })
            
            # Extract key AI concepts
            all_content = " ".join([r.get('content', '') for r in ai_results])
            key_concepts = self._extract_ai_concepts(all_content)
            
            return {
                "query": query,
                "domain": domain,
                "venue": venue,
                "year": year,
                "results": ai_results,
                "key_ai_concepts": key_concepts,
                "total_results": len(ai_results),
                "search_strategy": "ai_research_focused"
            }
            
        except Exception as e:
            return {"error": f"AI paper search failed: {str(e)}"}
    
    async def _analyze_ai_trends(self, technology: str, time_frame: str = None, industry: str = None) -> Dict[str, Any]:
        """Analyze AI technology trends and developments"""
        try:
            # Build comprehensive query for AI trends
            trend_query = f"{technology} AI trends development"
            if time_frame:
                trend_query += f" {time_frame}"
            if industry:
                trend_query += f" {industry}"
            
            # Add AI trend sources
            trend_query += " site:arxiv.org OR site:ai.googleblog.com OR site:openai.com OR site:deepmind.com"
            
            from tools.research_tools import web_search, analyze_sentiment
            results = await web_search(trend_query, num_results=6)
            
            # Analyze sentiment around the technology
            all_content = " ".join([r.get('content', '') for r in results])
            sentiment_analysis = analyze_sentiment(all_content)
            
            # Extract trend indicators
            trend_indicators = self._extract_ai_trend_indicators(all_content, technology)
            
            # Assess technology maturity
            maturity_assessment = self._assess_technology_maturity(all_content, technology)
            
            return {
                "technology": technology,
                "time_frame": time_frame,
                "industry": industry,
                "trend_data": results,
                "sentiment_analysis": sentiment_analysis,
                "trend_indicators": trend_indicators,
                "maturity_assessment": maturity_assessment,
                "analysis_type": "ai_technology_trends"
            }
            
        except Exception as e:
            return {"error": f"AI trend analysis failed: {str(e)}"}
    
    async def _evaluate_ai_model(self, model_name: str, task: str = None, metrics: str = None) -> Dict[str, Any]:
        """Evaluate AI model performance and capabilities"""
        try:
            # Build model evaluation query
            eval_query = f"{model_name} AI model performance evaluation"
            if task:
                eval_query += f" {task}"
            if metrics:
                eval_query += f" {metrics}"
            
            # Add benchmark and evaluation sources
            eval_query += " benchmark performance results"
            
            from tools.research_tools import web_search
            results = await web_search(eval_query, num_results=6)
            
            # Extract performance metrics
            performance_data = self._extract_performance_metrics(results, model_name)
            
            # Assess model capabilities
            capabilities = self._assess_model_capabilities(results, model_name)
            
            # Compare with baselines
            comparison_data = self._generate_model_comparison(results, model_name)
            
            return {
                "model_name": model_name,
                "task": task,
                "metrics": metrics,
                "evaluation_results": results,
                "performance_data": performance_data,
                "capabilities": capabilities,
                "comparison_data": comparison_data,
                "evaluation_summary": self._summarize_model_evaluation(performance_data, capabilities)
            }
            
        except Exception as e:
            return {"error": f"AI model evaluation failed: {str(e)}"}
    
    async def _analyze_ai_ethics(self, topic: str, context: str = None, stakeholders: str = None) -> Dict[str, Any]:
        """Analyze AI ethics, bias, and responsible AI considerations"""
        try:
            # Build ethics analysis query
            ethics_query = f"AI ethics {topic} responsible AI"
            if context:
                ethics_query += f" {context}"
            if stakeholders:
                ethics_query += f" {stakeholders}"
            
            # Add ethics and policy sources
            ethics_query += " bias fairness transparency accountability"
            
            from tools.research_tools import web_search
            results = await web_search(ethics_query, num_results=6)
            
            # Analyze ethical considerations
            ethical_analysis = self._analyze_ethical_considerations(results, topic)
            
            # Identify potential risks and mitigation strategies
            risk_analysis = self._identify_ethical_risks(results, topic)
            
            # Generate recommendations
            recommendations = self._generate_ethics_recommendations(ethical_analysis, risk_analysis)
            
            return {
                "topic": topic,
                "context": context,
                "stakeholders": stakeholders,
                "research_results": results,
                "ethical_analysis": ethical_analysis,
                "risk_analysis": risk_analysis,
                "recommendations": recommendations,
                "analysis_type": "ai_ethics_analysis"
            }
            
        except Exception as e:
            return {"error": f"AI ethics analysis failed: {str(e)}"}
    
    async def _research_ai_applications(self, industry: str, use_case: str = None, maturity: str = None) -> Dict[str, Any]:
        """Research AI applications in specific industries or domains"""
        try:
            # Build application research query
            app_query = f"AI applications {industry}"
            if use_case:
                app_query += f" {use_case}"
            if maturity:
                app_query += f" {maturity}"
            
            # Add industry and application sources
            app_query += " implementation case study deployment"
            
            from tools.research_tools import web_search
            results = await web_search(app_query, num_results=6)
            
            # Analyze application landscape
            application_analysis = self._analyze_application_landscape(results, industry)
            
            # Assess implementation challenges
            challenges = self._identify_implementation_challenges(results, industry)
            
            # Evaluate market readiness
            market_analysis = self._assess_market_readiness(results, industry)
            
            return {
                "industry": industry,
                "use_case": use_case,
                "maturity": maturity,
                "research_results": results,
                "application_analysis": application_analysis,
                "implementation_challenges": challenges,
                "market_analysis": market_analysis,
                "analysis_type": "ai_application_research"
            }
            
        except Exception as e:
            return {"error": f"AI application research failed: {str(e)}"}
    
    def _is_ai_research_source(self, url: str) -> bool:
        """Check if URL is from a reputable AI research source"""
        ai_domains = [
            'arxiv.org', 'papers.nips.cc', 'openreview.net', 'aclanthology.org',
            'ai.googleblog.com', 'openai.com', 'deepmind.com', 'research.facebook.com',
            'microsoft.com/research', 'ai.stanford.edu', 'csail.mit.edu'
        ]
        return any(domain in url.lower() for domain in ai_domains)
    
    def _classify_ai_source(self, url: str) -> str:
        """Classify the type of AI research source"""
        if 'arxiv.org' in url.lower():
            return 'preprint_server'
        elif any(conf in url.lower() for conf in ['nips.cc', 'openreview.net']):
            return 'conference_proceedings'
        elif any(blog in url.lower() for blog in ['googleblog.com', 'openai.com', 'deepmind.com']):
            return 'industry_research_blog'
        elif 'aclanthology.org' in url.lower():
            return 'nlp_conference_proceedings'
        else:
            return 'ai_research_website'
    
    def _assess_technical_level(self, content: str) -> str:
        """Assess the technical level of the content"""
        technical_indicators = [
            'algorithm', 'neural network', 'gradient', 'optimization', 'loss function',
            'architecture', 'hyperparameter', 'regularization', 'backpropagation'
        ]
        
        content_lower = content.lower()
        technical_count = sum(1 for indicator in technical_indicators if indicator in content_lower)
        
        if technical_count >= 5:
            return 'highly_technical'
        elif technical_count >= 3:
            return 'moderately_technical'
        else:
            return 'general_audience'
    
    def _extract_ai_concepts(self, content: str) -> List[str]:
        """Extract key AI concepts from content"""
        ai_concepts = [
            'machine learning', 'deep learning', 'neural network', 'transformer',
            'attention mechanism', 'convolutional neural network', 'recurrent neural network',
            'reinforcement learning', 'supervised learning', 'unsupervised learning',
            'natural language processing', 'computer vision', 'generative ai',
            'large language model', 'foundation model', 'multimodal'
        ]
        
        found_concepts = []
        content_lower = content.lower()
        
        for concept in ai_concepts:
            if concept in content_lower:
                found_concepts.append(concept)
        
        return found_concepts[:10]  # Return top 10 concepts
    
    def _extract_ai_trend_indicators(self, content: str, technology: str) -> Dict[str, Any]:
        """Extract AI trend indicators from content"""
        indicators = {
            "adoption_trends": [],
            "research_activity": [],
            "industry_interest": [],
            "technical_progress": []
        }
        
        content_lower = content.lower()
        
        # Adoption trends
        if any(word in content_lower for word in ['adoption', 'deployment', 'implementation', 'production']):
            indicators["adoption_trends"].append(f"Increasing adoption of {technology}")
        
        # Research activity
        if any(word in content_lower for word in ['research', 'paper', 'study', 'breakthrough']):
            indicators["research_activity"].append(f"Active research in {technology}")
        
        # Industry interest
        if any(word in content_lower for word in ['investment', 'funding', 'commercial', 'market']):
            indicators["industry_interest"].append(f"Growing industry interest in {technology}")
        
        # Technical progress
        if any(word in content_lower for word in ['improvement', 'advancement', 'innovation', 'state-of-the-art']):
            indicators["technical_progress"].append(f"Technical progress in {technology}")
        
        return indicators
    
    def _assess_technology_maturity(self, content: str, technology: str) -> Dict[str, Any]:
        """Assess the maturity level of an AI technology"""
        maturity_indicators = {
            "research_stage": 0,
            "prototype_stage": 0,
            "production_stage": 0,
            "mature_stage": 0
        }
        
        content_lower = content.lower()
        
        # Research stage indicators
        if any(word in content_lower for word in ['experimental', 'proof of concept', 'early research']):
            maturity_indicators["research_stage"] += 1
        
        # Prototype stage indicators
        if any(word in content_lower for word in ['prototype', 'pilot', 'beta', 'testing']):
            maturity_indicators["prototype_stage"] += 1
        
        # Production stage indicators
        if any(word in content_lower for word in ['production', 'deployment', 'commercial', 'enterprise']):
            maturity_indicators["production_stage"] += 1
        
        # Mature stage indicators
        if any(word in content_lower for word in ['established', 'standard', 'widespread', 'mature']):
            maturity_indicators["mature_stage"] += 1
        
        # Determine overall maturity
        max_stage = max(maturity_indicators, key=maturity_indicators.get)
        
        return {
            "maturity_level": max_stage.replace("_stage", ""),
            "indicators": maturity_indicators,
            "assessment": f"{technology} appears to be in {max_stage.replace('_', ' ')}"
        }
    
    async def specialized_task(self, task: str, **kwargs) -> Dict[str, Any]:
        """Handle specialized AI research tasks"""
        task_type = kwargs.get('task_type', 'general_research')
        
        if task_type == 'ai_paper_search':
            return await self._search_ai_papers(
                task,
                kwargs.get('domain'),
                kwargs.get('venue'),
                kwargs.get('year')
            )
        elif task_type == 'ai_trend_analysis':
            return await self._analyze_ai_trends(
                task,
                kwargs.get('time_frame'),
                kwargs.get('industry')
            )
        elif task_type == 'model_evaluation':
            return await self._evaluate_ai_model(
                task,
                kwargs.get('task'),
                kwargs.get('metrics')
            )
        elif task_type == 'ethics_analysis':
            return await self._analyze_ai_ethics(
                task,
                kwargs.get('context'),
                kwargs.get('stakeholders')
            )
        elif task_type == 'application_research':
            return await self._research_ai_applications(
                task,
                kwargs.get('use_case'),
                kwargs.get('maturity')
            )
        else:
            # Default to general AI research
            response = await self.process_message(task, kwargs)
            return {"response": response, "task_type": "general_ai_research"}
    
    def _extract_performance_metrics(self, results: List[Dict[str, Any]], model_name: str) -> Dict[str, Any]:
        """Extract performance metrics from search results"""
        metrics = {
            "accuracy_scores": [],
            "benchmark_results": [],
            "performance_summary": f"Performance data for {model_name}"
        }
        
        # This would be enhanced with actual benchmark database integration
        all_content = " ".join([r.get('content', '') for r in results])
        
        # Look for numerical performance indicators
        import re
        accuracy_pattern = r'(\d+\.?\d*)%?\s*accuracy'
        accuracies = re.findall(accuracy_pattern, all_content.lower())
        metrics["accuracy_scores"] = [float(acc) for acc in accuracies[:5]]  # Top 5 scores
        
        return metrics
    
    def _assess_model_capabilities(self, results: List[Dict[str, Any]], model_name: str) -> Dict[str, Any]:
        """Assess model capabilities from search results"""
        capabilities = {
            "strengths": [],
            "limitations": [],
            "use_cases": [],
            "technical_specs": {}
        }
        
        all_content = " ".join([r.get('content', '') for r in results])
        content_lower = all_content.lower()
        
        # Identify strengths
        if any(word in content_lower for word in ['excellent', 'superior', 'state-of-the-art', 'breakthrough']):
            capabilities["strengths"].append("High performance capabilities")
        
        # Identify limitations
        if any(word in content_lower for word in ['limitation', 'weakness', 'challenge', 'issue']):
            capabilities["limitations"].append("Some limitations identified")
        
        return capabilities
    
    def _generate_model_comparison(self, results: List[Dict[str, Any]], model_name: str) -> Dict[str, Any]:
        """Generate model comparison data"""
        comparison = {
            "baseline_models": [],
            "competitive_analysis": f"Comparison analysis for {model_name}",
            "relative_performance": "Analysis pending"
        }
        
        # This would be enhanced with actual model comparison databases
        return comparison
    
    def _summarize_model_evaluation(self, performance_data: Dict[str, Any], capabilities: Dict[str, Any]) -> str:
        """Summarize model evaluation results"""
        summary_parts = []
        
        if performance_data.get("accuracy_scores"):
            avg_accuracy = sum(performance_data["accuracy_scores"]) / len(performance_data["accuracy_scores"])
            summary_parts.append(f"Average accuracy: {avg_accuracy:.2f}%")
        
        if capabilities.get("strengths"):
            summary_parts.append(f"Key strengths: {', '.join(capabilities['strengths'])}")
        
        if capabilities.get("limitations"):
            summary_parts.append(f"Limitations: {', '.join(capabilities['limitations'])}")
        
        return "; ".join(summary_parts) if summary_parts else "Evaluation summary pending detailed analysis"
    
    def _analyze_ethical_considerations(self, results: List[Dict[str, Any]], topic: str) -> Dict[str, Any]:
        """Analyze ethical considerations from search results"""
        analysis = {
            "key_concerns": [],
            "stakeholder_impact": [],
            "ethical_principles": [],
            "regulatory_considerations": []
        }
        
        all_content = " ".join([r.get('content', '') for r in results])
        content_lower = all_content.lower()
        
        # Identify key ethical concerns
        ethical_keywords = ['bias', 'fairness', 'transparency', 'accountability', 'privacy', 'discrimination']
        for keyword in ethical_keywords:
            if keyword in content_lower:
                analysis["key_concerns"].append(f"{keyword.capitalize()} considerations")
        
        return analysis
    
    def _identify_ethical_risks(self, results: List[Dict[str, Any]], topic: str) -> Dict[str, Any]:
        """Identify potential ethical risks"""
        risks = {
            "high_risk_areas": [],
            "medium_risk_areas": [],
            "mitigation_strategies": []
        }
        
        # This would be enhanced with ethical risk assessment frameworks
        risks["mitigation_strategies"].append("Implement bias detection and mitigation techniques")
        risks["mitigation_strategies"].append("Ensure transparency and explainability")
        risks["mitigation_strategies"].append("Establish ethical review processes")
        
        return risks
    
    def _generate_ethics_recommendations(self, ethical_analysis: Dict[str, Any], risk_analysis: Dict[str, Any]) -> List[str]:
        """Generate ethics recommendations"""
        recommendations = [
            "Conduct regular bias audits and fairness assessments",
            "Implement transparent AI decision-making processes",
            "Establish clear accountability frameworks",
            "Engage diverse stakeholders in AI development",
            "Follow established AI ethics guidelines and principles"
        ]
        
        return recommendations
    
    def _analyze_application_landscape(self, results: List[Dict[str, Any]], industry: str) -> Dict[str, Any]:
        """Analyze AI application landscape in an industry"""
        landscape = {
            "current_applications": [],
            "emerging_applications": [],
            "adoption_level": "Assessment pending",
            "key_players": []
        }
        
        # This would be enhanced with industry-specific AI application databases
        all_content = " ".join([r.get('content', '') for r in results])
        content_lower = all_content.lower()
        
        if 'automation' in content_lower:
            landscape["current_applications"].append("Process automation")
        if 'prediction' in content_lower or 'forecasting' in content_lower:
            landscape["current_applications"].append("Predictive analytics")
        if 'recommendation' in content_lower:
            landscape["current_applications"].append("Recommendation systems")
        
        return landscape
    
    def _identify_implementation_challenges(self, results: List[Dict[str, Any]], industry: str) -> List[str]:
        """Identify implementation challenges for AI in an industry"""
        challenges = [
            "Data quality and availability",
            "Integration with existing systems",
            "Regulatory compliance requirements",
            "Skills and talent acquisition",
            "Change management and adoption"
        ]
        
        # This would be enhanced with industry-specific challenge analysis
        return challenges
    
    def _assess_market_readiness(self, results: List[Dict[str, Any]], industry: str) -> Dict[str, Any]:
        """Assess market readiness for AI adoption in an industry"""
        readiness = {
            "readiness_level": "Moderate",
            "market_drivers": [],
            "barriers": [],
            "timeline_estimate": "2-5 years for widespread adoption"
        }
        
        # This would be enhanced with market analysis data
        readiness["market_drivers"].append("Competitive pressure")
        readiness["market_drivers"].append("Cost reduction opportunities")
        readiness["barriers"].append("Regulatory uncertainty")
        readiness["barriers"].append("Technical complexity")
        
        return readiness
