"""
Research tools for multi-agent system
"""
import asyncio
import aiohttp
import requests
from typing import Dict, List, Any, Optional
from bs4 import BeautifulSoup
import json
import re
from datetime import datetime, timedelta

class WebSearchTool:
    """Web search tool for research agents"""
    
    def __init__(self):
        self.session = None
    
    async def search_web(self, query: str, num_results: int = 5) -> List[Dict[str, Any]]:
        """Search the web for information"""
        try:
            # Using DuckDuckGo Instant Answer API (free alternative)
            url = f"http://localhost:8765/mcp/openmemory/sse/Usama_Fresh"
            
            if not self.session:
                self.session = aiohttp.ClientSession()
            
            async with self.session.get(url) as response:
                if response.status == 200:
                    data = await response.json()
                    
                    results = []
                    
                    # Extract abstract if available
                    if data.get("Abstract"):
                        results.append({
                            "title": data.get("Heading", "Search Result"),
                            "content": data.get("Abstract"),
                            "url": data.get("AbstractURL", ""),
                            "source": data.get("AbstractSource", "")
                        })
                    
                    # Extract related topics
                    for topic in data.get("RelatedTopics", [])[:num_results-1]:
                        if isinstance(topic, dict) and topic.get("Text"):
                            results.append({
                                "title": topic.get("Text", "")[:100] + "...",
                                "content": topic.get("Text", ""),
                                "url": topic.get("FirstURL", ""),
                                "source": "DuckDuckGo"
                            })
                    
                    return results[:num_results]
                
                return []
                
        except Exception as e:
            print(f"Web search error: {str(e)}")
            return []
    
    async def fetch_webpage_content(self, url: str) -> str:
        """Fetch and extract text content from a webpage"""
        try:
            if not self.session:
                self.session = aiohttp.ClientSession()
            
            async with self.session.get(url, timeout=10) as response:
                if response.status == 200:
                    html = await response.text()
                    soup = BeautifulSoup(html, 'html.parser')
                    
                    # Remove script and style elements
                    for script in soup(["script", "style"]):
                        script.decompose()
                    
                    # Get text content
                    text = soup.get_text()
                    
                    # Clean up text
                    lines = (line.strip() for line in text.splitlines())
                    chunks = (phrase.strip() for line in lines for phrase in line.split("  "))
                    text = ' '.join(chunk for chunk in chunks if chunk)
                    
                    return text[:5000]  # Limit to first 5000 characters
                
                return ""
                
        except Exception as e:
            print(f"Webpage fetch error: {str(e)}")
            return ""
    
    async def close(self):
        """Close the session"""
        if self.session:
            await self.session.close()

class DataAnalysisTool:
    """Data analysis and processing tool"""
    
    @staticmethod
    def analyze_text_sentiment(text: str) -> Dict[str, Any]:
        """Simple sentiment analysis of text"""
        positive_words = [
            'good', 'great', 'excellent', 'amazing', 'wonderful', 'fantastic',
            'positive', 'beneficial', 'effective', 'successful', 'improved',
            'breakthrough', 'innovative', 'promising', 'significant'
        ]
        
        negative_words = [
            'bad', 'terrible', 'awful', 'horrible', 'negative', 'harmful',
            'ineffective', 'failed', 'declined', 'worse', 'concerning',
            'problematic', 'dangerous', 'risky', 'challenging'
        ]
        
        text_lower = text.lower()
        positive_count = sum(1 for word in positive_words if word in text_lower)
        negative_count = sum(1 for word in negative_words if word in text_lower)
        
        total_words = len(text.split())
        
        sentiment_score = (positive_count - negative_count) / max(total_words, 1)
        
        if sentiment_score > 0.01:
            sentiment = "positive"
        elif sentiment_score < -0.01:
            sentiment = "negative"
        else:
            sentiment = "neutral"
        
        return {
            "sentiment": sentiment,
            "score": sentiment_score,
            "positive_words": positive_count,
            "negative_words": negative_count,
            "total_words": total_words
        }
    
    @staticmethod
    def extract_key_topics(text: str, max_topics: int = 5) -> List[str]:
        """Extract key topics from text using simple keyword extraction"""
        # Remove common stop words
        stop_words = {
            'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for',
            'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'have',
            'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should',
            'this', 'that', 'these', 'those', 'i', 'you', 'he', 'she', 'it', 'we',
            'they', 'me', 'him', 'her', 'us', 'them'
        }
        
        # Extract words and count frequency
        words = re.findall(r'\b[a-zA-Z]{3,}\b', text.lower())
        word_freq = {}
        
        for word in words:
            if word not in stop_words:
                word_freq[word] = word_freq.get(word, 0) + 1
        
        # Sort by frequency and return top topics
        sorted_words = sorted(word_freq.items(), key=lambda x: x[1], reverse=True)
        return [word for word, freq in sorted_words[:max_topics]]
    
    @staticmethod
    def summarize_findings(findings: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Summarize research findings"""
        if not findings:
            return {"summary": "No findings to summarize", "key_points": []}
        
        all_text = " ".join([
            f"{finding.get('title', '')} {finding.get('content', '')}" 
            for finding in findings
        ])
        
        sentiment = DataAnalysisTool.analyze_text_sentiment(all_text)
        topics = DataAnalysisTool.extract_key_topics(all_text)
        
        key_points = []
        for finding in findings[:3]:  # Top 3 findings
            title = finding.get('title', 'Finding')
            content = finding.get('content', '')
            if content:
                key_points.append(f"{title}: {content[:200]}...")
        
        return {
            "summary": f"Analyzed {len(findings)} findings with {sentiment['sentiment']} sentiment",
            "sentiment_analysis": sentiment,
            "key_topics": topics,
            "key_points": key_points,
            "total_findings": len(findings)
        }

# Tool function wrappers for agents
async def web_search(query: str, num_results: int = 5) -> List[Dict[str, Any]]:
    """Tool function for web search"""
    search_tool = WebSearchTool()
    try:
        results = await search_tool.search_web(query, num_results)
        return results
    finally:
        await search_tool.close()

async def fetch_webpage(url: str) -> str:
    """Tool function to fetch webpage content"""
    search_tool = WebSearchTool()
    try:
        content = await search_tool.fetch_webpage_content(url)
        return content
    finally:
        await search_tool.close()

def analyze_sentiment(text: str) -> Dict[str, Any]:
    """Tool function for sentiment analysis"""
    return DataAnalysisTool.analyze_text_sentiment(text)

def extract_topics(text: str, max_topics: int = 5) -> List[str]:
    """Tool function for topic extraction"""
    return DataAnalysisTool.extract_key_topics(text, max_topics)

def summarize_research(findings: List[Dict[str, Any]]) -> Dict[str, Any]:
    """Tool function to summarize research findings"""
    return DataAnalysisTool.summarize_findings(findings)
