"""
Healthcare Research Agent - Specialized in medical research and health analysis
"""
import asyncio
from typing import Dict, List, Any, Optional
import sys
import os

# Add the parent directory to the path to import modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from agents.base_agent import BaseAgent
from tools.research_tools import extract_topics, summarize_research
from config import Config

class HealthcareAgent(BaseAgent):
    """Healthcare Research Agent specializing in medical research and health analysis"""
    
    def __init__(self):
        # Load system prompt
        prompt_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), "prompts", "healthcare_prompt.md")
        with open(prompt_path, 'r', encoding='utf-8') as f:
            system_prompt = f.read()
        
        super().__init__(
            agent_id="healthcare_agent",
            name="Healthcare Research Agent",
            description="Specializes in medical research, health trends, clinical studies, and healthcare analysis",
            system_prompt=system_prompt,
            tools=self._setup_healthcare_tools()
        )
    
    def _setup_healthcare_tools(self) -> List[Dict[str, Any]]:
        """Setup healthcare-specific tools"""
        healthcare_tools = [
            {
                "name": "search_medical_literature",
                "description": "Search for medical literature and research papers",
                "function": self._search_medical_literature,
                "parameters": {
                    "type": "object",
                    "properties": {
                        "query": {"type": "string", "description": "Medical research query"},
                        "focus_area": {"type": "string", "description": "Specific medical focus area (e.g., cardiology, oncology)"},
                        "study_type": {"type": "string", "description": "Type of study (e.g., clinical trial, meta-analysis)"}
                    },
                    "required": ["query"]
                }
            },
            {
                "name": "analyze_health_trends",
                "description": "Analyze health trends and epidemiological data",
                "function": self._analyze_health_trends,
                "parameters": {
                    "type": "object",
                    "properties": {
                        "condition": {"type": "string", "description": "Health condition or disease"},
                        "time_period": {"type": "string", "description": "Time period for trend analysis"},
                        "population": {"type": "string", "description": "Target population or demographic"}
                    },
                    "required": ["condition"]
                }
            },
            {
                "name": "evaluate_clinical_evidence",
                "description": "Evaluate the quality and reliability of clinical evidence",
                "function": self._evaluate_clinical_evidence,
                "parameters": {
                    "type": "object",
                    "properties": {
                        "study_description": {"type": "string", "description": "Description of the clinical study"},
                        "evidence_type": {"type": "string", "description": "Type of evidence (RCT, observational, etc.)"}
                    },
                    "required": ["study_description"]
                }
            },
            {
                "name": "assess_drug_information",
                "description": "Research and assess pharmaceutical drug information",
                "function": self._assess_drug_information,
                "parameters": {
                    "type": "object",
                    "properties": {
                        "drug_name": {"type": "string", "description": "Name of the drug or medication"},
                        "indication": {"type": "string", "description": "Medical indication or condition"},
                        "analysis_type": {"type": "string", "description": "Type of analysis (efficacy, safety, interactions)"}
                    },
                    "required": ["drug_name"]
                }
            }
        ]
        return healthcare_tools
    
    async def _search_medical_literature(self, query: str, focus_area: str = None, study_type: str = None) -> Dict[str, Any]:
        """Search for medical literature and research papers"""
        try:
            # Enhance query with medical terms
            enhanced_query = f"{query} medical research"
            if focus_area:
                enhanced_query += f" {focus_area}"
            if study_type:
                enhanced_query += f" {study_type}"
            
            # Add medical database terms
            enhanced_query += " site:pubmed.ncbi.nlm.nih.gov OR site:cochranelibrary.com OR site:nejm.org OR site:thelancet.com"
            
            # Use web search tool
            from tools.research_tools import web_search
            results = await web_search(enhanced_query, num_results=8)
            
            # Filter and enhance results for medical relevance
            medical_results = []
            for result in results:
                if self._is_medical_source(result.get('url', '')):
                    medical_results.append({
                        **result,
                        'medical_relevance': 'high',
                        'source_type': self._classify_medical_source(result.get('url', ''))
                    })
            
            # Extract key topics from medical literature
            all_content = " ".join([r.get('content', '') for r in medical_results])
            key_topics = extract_topics(all_content, max_topics=8)
            
            return {
                "query": query,
                "focus_area": focus_area,
                "study_type": study_type,
                "results": medical_results,
                "key_medical_topics": key_topics,
                "total_results": len(medical_results),
                "search_strategy": "medical_literature_focused"
            }
            
        except Exception as e:
            return {"error": f"Medical literature search failed: {str(e)}"}
    
    async def _analyze_health_trends(self, condition: str, time_period: str = None, population: str = None) -> Dict[str, Any]:
        """Analyze health trends and epidemiological data"""
        try:
            # Build comprehensive query for health trends
            trend_query = f"{condition} epidemiology trends"
            if time_period:
                trend_query += f" {time_period}"
            if population:
                trend_query += f" {population}"
            
            # Add epidemiological sources
            trend_query += " site:cdc.gov OR site:who.int OR site:nih.gov epidemiology statistics"
            
            from tools.research_tools import web_search, analyze_sentiment
            results = await web_search(trend_query, num_results=6)
            
            # Analyze sentiment of health trend discussions
            all_content = " ".join([r.get('content', '') for r in results])
            sentiment_analysis = analyze_sentiment(all_content)
            
            # Extract trend indicators
            trend_indicators = self._extract_trend_indicators(all_content)
            
            return {
                "condition": condition,
                "time_period": time_period,
                "population": population,
                "trend_data": results,
                "sentiment_analysis": sentiment_analysis,
                "trend_indicators": trend_indicators,
                "analysis_type": "epidemiological_trends"
            }
            
        except Exception as e:
            return {"error": f"Health trend analysis failed: {str(e)}"}
    
    async def _evaluate_clinical_evidence(self, study_description: str, evidence_type: str = None) -> Dict[str, Any]:
        """Evaluate the quality and reliability of clinical evidence"""
        try:
            # Analyze the study description for quality indicators
            quality_indicators = self._assess_study_quality(study_description, evidence_type)
            
            # Search for related evidence and validation
            validation_query = f"clinical evidence validation {study_description[:100]}"
            from tools.research_tools import web_search
            validation_results = await web_search(validation_query, num_results=4)
            
            # Determine evidence level based on study type
            evidence_level = self._determine_evidence_level(evidence_type, study_description)
            
            return {
                "study_description": study_description,
                "evidence_type": evidence_type,
                "quality_indicators": quality_indicators,
                "evidence_level": evidence_level,
                "validation_sources": validation_results,
                "reliability_score": quality_indicators.get("overall_score", 0),
                "recommendations": self._generate_evidence_recommendations(quality_indicators)
            }
            
        except Exception as e:
            return {"error": f"Clinical evidence evaluation failed: {str(e)}"}
    
    async def _assess_drug_information(self, drug_name: str, indication: str = None, analysis_type: str = None) -> Dict[str, Any]:
        """Research and assess pharmaceutical drug information"""
        try:
            # Build comprehensive drug research query
            drug_query = f"{drug_name} pharmaceutical"
            if indication:
                drug_query += f" {indication}"
            if analysis_type:
                drug_query += f" {analysis_type}"
            
            # Add pharmaceutical database sources
            drug_query += " site:fda.gov OR site:drugs.com OR site:rxlist.com OR site:clinicaltrials.gov"
            
            from tools.research_tools import web_search
            drug_results = await web_search(drug_query, num_results=6)
            
            # Analyze drug safety and efficacy information
            safety_analysis = self._analyze_drug_safety(drug_results)
            efficacy_analysis = self._analyze_drug_efficacy(drug_results)
            
            return {
                "drug_name": drug_name,
                "indication": indication,
                "analysis_type": analysis_type,
                "research_results": drug_results,
                "safety_analysis": safety_analysis,
                "efficacy_analysis": efficacy_analysis,
                "regulatory_status": self._extract_regulatory_info(drug_results),
                "clinical_considerations": self._generate_clinical_considerations(drug_results)
            }
            
        except Exception as e:
            return {"error": f"Drug information assessment failed: {str(e)}"}
    
    def _is_medical_source(self, url: str) -> bool:
        """Check if URL is from a reputable medical source"""
        medical_domains = [
            'pubmed.ncbi.nlm.nih.gov', 'cochranelibrary.com', 'nejm.org', 'thelancet.com',
            'bmj.com', 'jamanetwork.com', 'nature.com', 'cell.com', 'sciencedirect.com',
            'cdc.gov', 'who.int', 'nih.gov', 'fda.gov', 'mayoclinic.org'
        ]
        return any(domain in url.lower() for domain in medical_domains)
    
    def _classify_medical_source(self, url: str) -> str:
        """Classify the type of medical source"""
        if 'pubmed' in url.lower():
            return 'peer_reviewed_database'
        elif any(domain in url.lower() for domain in ['nejm.org', 'thelancet.com', 'bmj.com']):
            return 'medical_journal'
        elif any(domain in url.lower() for domain in ['cdc.gov', 'who.int', 'nih.gov']):
            return 'government_health_agency'
        elif 'cochrane' in url.lower():
            return 'systematic_review_database'
        else:
            return 'medical_website'
    
    def _extract_trend_indicators(self, content: str) -> Dict[str, Any]:
        """Extract health trend indicators from content"""
        # Simple trend indicator extraction
        indicators = {
            "increasing_trends": [],
            "decreasing_trends": [],
            "stable_trends": [],
            "emerging_concerns": []
        }
        
        content_lower = content.lower()
        
        # Look for trend keywords
        if any(word in content_lower for word in ['increasing', 'rising', 'growing', 'surge']):
            indicators["increasing_trends"].append("Potential increasing trend identified")
        
        if any(word in content_lower for word in ['decreasing', 'declining', 'falling', 'reduction']):
            indicators["decreasing_trends"].append("Potential decreasing trend identified")
        
        if any(word in content_lower for word in ['stable', 'consistent', 'maintained']):
            indicators["stable_trends"].append("Stable trend pattern identified")
        
        if any(word in content_lower for word in ['emerging', 'new', 'novel', 'unprecedented']):
            indicators["emerging_concerns"].append("Emerging health concern identified")
        
        return indicators
    
    async def specialized_task(self, task: str, **kwargs) -> Dict[str, Any]:
        """Handle specialized healthcare research tasks"""
        task_type = kwargs.get('task_type', 'general_research')
        
        if task_type == 'medical_literature_review':
            return await self._search_medical_literature(
                task, 
                kwargs.get('focus_area'), 
                kwargs.get('study_type')
            )
        elif task_type == 'health_trend_analysis':
            return await self._analyze_health_trends(
                task,
                kwargs.get('time_period'),
                kwargs.get('population')
            )
        elif task_type == 'clinical_evidence_evaluation':
            return await self._evaluate_clinical_evidence(
                task,
                kwargs.get('evidence_type')
            )
        elif task_type == 'drug_assessment':
            return await self._assess_drug_information(
                task,
                kwargs.get('indication'),
                kwargs.get('analysis_type')
            )
        else:
            # Default to general healthcare research
            response = await self.process_message(task, kwargs)
            return {"response": response, "task_type": "general_healthcare_research"}
    
    def _assess_study_quality(self, study_description: str, evidence_type: str) -> Dict[str, Any]:
        """Assess the quality of a clinical study"""
        # Simplified quality assessment
        quality_score = 5  # Base score
        indicators = []
        
        study_lower = study_description.lower()
        
        # Positive quality indicators
        if 'randomized' in study_lower:
            quality_score += 2
            indicators.append("Randomized design")
        
        if 'controlled' in study_lower:
            quality_score += 2
            indicators.append("Controlled study")
        
        if 'double-blind' in study_lower or 'blinded' in study_lower:
            quality_score += 1
            indicators.append("Blinded design")
        
        if 'large sample' in study_lower or 'multicenter' in study_lower:
            quality_score += 1
            indicators.append("Large or multicenter study")
        
        # Evidence type scoring
        if evidence_type:
            if evidence_type.lower() in ['meta-analysis', 'systematic review']:
                quality_score += 3
            elif evidence_type.lower() in ['rct', 'randomized controlled trial']:
                quality_score += 2
        
        return {
            "overall_score": min(quality_score, 10),  # Cap at 10
            "quality_indicators": indicators,
            "evidence_type": evidence_type,
            "assessment_notes": f"Quality assessment based on study design and methodology"
        }
    
    def _determine_evidence_level(self, evidence_type: str, study_description: str) -> str:
        """Determine the level of evidence"""
        if not evidence_type:
            return "Level V - Expert opinion"
        
        evidence_type_lower = evidence_type.lower()
        
        if 'meta-analysis' in evidence_type_lower or 'systematic review' in evidence_type_lower:
            return "Level I - Systematic review/Meta-analysis"
        elif 'rct' in evidence_type_lower or 'randomized controlled' in evidence_type_lower:
            return "Level II - Randomized controlled trial"
        elif 'cohort' in evidence_type_lower:
            return "Level III - Cohort study"
        elif 'case-control' in evidence_type_lower:
            return "Level IV - Case-control study"
        else:
            return "Level V - Case series/Expert opinion"
    
    def _generate_evidence_recommendations(self, quality_indicators: Dict[str, Any]) -> List[str]:
        """Generate recommendations based on evidence quality"""
        score = quality_indicators.get("overall_score", 0)
        recommendations = []
        
        if score >= 8:
            recommendations.append("High-quality evidence - suitable for clinical decision making")
        elif score >= 6:
            recommendations.append("Moderate-quality evidence - consider with other evidence")
        elif score >= 4:
            recommendations.append("Low-quality evidence - interpret with caution")
        else:
            recommendations.append("Very low-quality evidence - limited clinical applicability")
        
        recommendations.append("Always consider patient-specific factors and clinical context")
        recommendations.append("Consult current clinical guidelines and expert opinion")
        
        return recommendations
    
    def _analyze_drug_safety(self, drug_results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze drug safety information from search results"""
        safety_info = {
            "safety_profile": "Information pending analysis",
            "common_side_effects": [],
            "serious_adverse_events": [],
            "contraindications": [],
            "safety_score": 5  # Neutral score
        }
        
        # This would be enhanced with actual drug safety database integration
        all_content = " ".join([r.get('content', '') for r in drug_results])
        content_lower = all_content.lower()
        
        # Look for safety keywords
        if any(word in content_lower for word in ['safe', 'well-tolerated', 'minimal side effects']):
            safety_info["safety_score"] += 1
        
        if any(word in content_lower for word in ['warning', 'serious', 'adverse', 'contraindicated']):
            safety_info["safety_score"] -= 1
        
        safety_info["safety_score"] = max(1, min(safety_info["safety_score"], 10))
        
        return safety_info
    
    def _analyze_drug_efficacy(self, drug_results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze drug efficacy information from search results"""
        efficacy_info = {
            "efficacy_profile": "Information pending analysis",
            "clinical_outcomes": [],
            "response_rates": [],
            "efficacy_score": 5  # Neutral score
        }
        
        # This would be enhanced with actual clinical trial database integration
        all_content = " ".join([r.get('content', '') for r in drug_results])
        content_lower = all_content.lower()
        
        # Look for efficacy keywords
        if any(word in content_lower for word in ['effective', 'significant improvement', 'beneficial']):
            efficacy_info["efficacy_score"] += 1
        
        if any(word in content_lower for word in ['ineffective', 'no significant', 'limited benefit']):
            efficacy_info["efficacy_score"] -= 1
        
        efficacy_info["efficacy_score"] = max(1, min(efficacy_info["efficacy_score"], 10))
        
        return efficacy_info
    
    def _extract_regulatory_info(self, drug_results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Extract regulatory information about the drug"""
        regulatory_info = {
            "fda_status": "Unknown",
            "approval_date": "Unknown",
            "indications": [],
            "regulatory_notes": []
        }
        
        # This would be enhanced with actual FDA database integration
        all_content = " ".join([r.get('content', '') for r in drug_results])
        content_lower = all_content.lower()
        
        if 'fda approved' in content_lower:
            regulatory_info["fda_status"] = "FDA Approved"
        elif 'fda' in content_lower and 'pending' in content_lower:
            regulatory_info["fda_status"] = "Pending FDA Review"
        
        return regulatory_info
    
    def _generate_clinical_considerations(self, drug_results: List[Dict[str, Any]]) -> List[str]:
        """Generate clinical considerations for drug use"""
        considerations = [
            "Consult current prescribing information and clinical guidelines",
            "Consider patient-specific factors including comorbidities and concurrent medications",
            "Monitor for therapeutic response and adverse effects",
            "Ensure appropriate dosing based on patient characteristics",
            "Consider drug interactions and contraindications"
        ]
        
        return considerations
