"""
Mem0 MCP (Model Context Protocol) integration for multi-agent memory management
"""
import asyncio
import httpx
import json
from typing import Dict, List, Any, Optional
from datetime import datetime
import uuid
from config import Config

class Mem0MCPClient:
    """Client for connecting to Mem0 MCP server"""
    
    def __init__(self, server_url: str = "http://localhost:8762/mcp/openmemory/sse/Usama_Fresh"):
        self.server_url = server_url
        self.session = None
        self.connected = False
        
    async def connect(self):
        """Connect to the Mem0 MCP server"""
        try:
            self.session = httpx.AsyncClient(timeout=30.0)
            # Test connection
            response = await self.session.get(f"{self.server_url}/health", timeout=5.0)
            if response.status_code == 200:
                self.connected = True
                print("✅ Connected to Mem0 MCP server")
                return True
        except Exception as e:
            print(f"❌ Failed to connect to Mem0 MCP server: {str(e)}")
            # Try alternative connection method
            try:
                self.session = httpx.AsyncClient(timeout=30.0)
                self.connected = True
                print("✅ Mem0 MCP client initialized (assuming server is running)")
                return True
            except Exception as e2:
                print(f"❌ Failed to initialize Mem0 MCP client: {str(e2)}")
        
        return False
    
    async def disconnect(self):
        """Disconnect from the Mem0 MCP server"""
        if self.session:
            await self.session.aclose()
            self.connected = False
    
    async def call_tool(self, tool_name: str, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """Call a tool on the Mem0 MCP server"""
        if not self.connected or not self.session:
            await self.connect()
        
        try:
            # Prepare the MCP tool call request
            request_data = {
                "method": "tools/call",
                "params": {
                    "name": tool_name,
                    "arguments": arguments
                }
            }
            
            # Make the request to the MCP server
            response = await self.session.post(
                f"{self.server_url}/call",
                json=request_data,
                headers={"Content-Type": "application/json"}
            )
            
            if response.status_code == 200:
                result = response.json()
                return {"success": True, "data": result}
            else:
                return {"success": False, "error": f"HTTP {response.status_code}: {response.text}"}
                
        except Exception as e:
            print(f"❌ Error calling Mem0 MCP tool {tool_name}: {str(e)}")
            return {"success": False, "error": str(e)}
    
    async def add_memories(self, memories: List[str], user_id: str = None) -> Dict[str, Any]:
        """Add memories to Mem0"""
        arguments = {
            "memories": memories
        }
        if user_id:
            arguments["user_id"] = user_id
            
        return await self.call_tool("add_memories", arguments)
    
    async def search_memory(self, query: str, user_id: str = None, limit: int = 5) -> Dict[str, Any]:
        """Search memories in Mem0"""
        arguments = {
            "query": query,
            "limit": limit
        }
        if user_id:
            arguments["user_id"] = user_id
            
        return await self.call_tool("search_memory", arguments)
    
    async def list_memories(self, user_id: str = None, limit: int = 100) -> Dict[str, Any]:
        """List all memories from Mem0"""
        arguments = {
            "limit": limit
        }
        if user_id:
            arguments["user_id"] = user_id
            
        return await self.call_tool("list_memories", arguments)
    
    async def delete_all_memories(self, user_id: str = None) -> Dict[str, Any]:
        """Delete all memories from Mem0"""
        arguments = {}
        if user_id:
            arguments["user_id"] = user_id
            
        return await self.call_tool("delete_all_memories", arguments)

# Global Mem0 MCP client instance
mem0_client = Mem0MCPClient()

class Mem0AgentMemoryManager:
    """Memory manager for individual agents using Mem0 MCP"""
    
    def __init__(self, agent_id: str):
        self.agent_id = agent_id
        self.user_id = f"agent_{agent_id}"
        self.client = mem0_client
        
    async def ensure_connected(self):
        """Ensure connection to Mem0 MCP server"""
        if not self.client.connected:
            await self.client.connect()
    
    async def store_memory(self, content: str, metadata: Dict[str, Any] = None) -> str:
        """Store a memory for the agent using Mem0"""
        try:
            await self.ensure_connected()
            
            # Enhance content with metadata and agent context
            enhanced_content = content
            if metadata:
                context_info = []
                for key, value in metadata.items():
                    context_info.append(f"{key}: {value}")
                if context_info:
                    enhanced_content += f" [Context: {', '.join(context_info)}]"
            
            # Add agent identification
            enhanced_content += f" [Agent: {self.agent_id}]"
            
            # Store in Mem0
            result = await self.client.add_memories([enhanced_content], self.user_id)
            
            if result.get("success"):
                memory_id = str(uuid.uuid4())  # Generate local ID for compatibility
                print(f"✅ Stored memory for {self.agent_id}: {content[:50]}...")
                return memory_id
            else:
                print(f"❌ Failed to store memory for {self.agent_id}: {result.get('error')}")
                return ""
                
        except Exception as e:
            print(f"❌ Error storing memory for agent {self.agent_id}: {str(e)}")
            return ""
    
    async def retrieve_memories(self, query: str, limit: int = 5) -> List[Dict[str, Any]]:
        """Retrieve relevant memories for the agent using Mem0"""
        try:
            await self.ensure_connected()
            
            # Search memories in Mem0
            result = await self.client.search_memory(query, self.user_id, limit)
            
            if result.get("success"):
                memories_data = result.get("data", {})
                memories = memories_data.get("memories", []) if isinstance(memories_data, dict) else []
                
                # Convert Mem0 format to our expected format
                formatted_memories = []
                for i, memory in enumerate(memories):
                    if isinstance(memory, dict):
                        formatted_memory = {
                            "id": memory.get("id", str(uuid.uuid4())),
                            "content": memory.get("memory", memory.get("content", "")),
                            "metadata": {
                                "agent_id": self.agent_id,
                                "score": memory.get("score", 0.0),
                                "mem0_id": memory.get("id")
                            },
                            "timestamp": memory.get("created_at", datetime.now().isoformat()),
                            "relevance_score": memory.get("score", 0.0)
                        }
                    else:
                        # Handle string format
                        formatted_memory = {
                            "id": str(uuid.uuid4()),
                            "content": str(memory),
                            "metadata": {"agent_id": self.agent_id},
                            "timestamp": datetime.now().isoformat(),
                            "relevance_score": 1.0
                        }
                    formatted_memories.append(formatted_memory)
                
                print(f"✅ Retrieved {len(formatted_memories)} memories for {self.agent_id}")
                return formatted_memories
            else:
                print(f"❌ Failed to retrieve memories for {self.agent_id}: {result.get('error')}")
                return []
                
        except Exception as e:
            print(f"❌ Error retrieving memories for agent {self.agent_id}: {str(e)}")
            return []
    
    async def get_all_memories(self) -> List[Dict[str, Any]]:
        """Get all memories for the agent using Mem0"""
        try:
            await self.ensure_connected()
            
            result = await self.client.list_memories(self.user_id, limit=1000)
            
            if result.get("success"):
                memories_data = result.get("data", {})
                memories = memories_data.get("memories", []) if isinstance(memories_data, dict) else []
                
                # Convert to our format
                formatted_memories = []
                for memory in memories:
                    if isinstance(memory, dict):
                        formatted_memory = {
                            "id": memory.get("id", str(uuid.uuid4())),
                            "content": memory.get("memory", memory.get("content", "")),
                            "metadata": {
                                "agent_id": self.agent_id,
                                "mem0_id": memory.get("id")
                            },
                            "timestamp": memory.get("created_at", datetime.now().isoformat())
                        }
                    else:
                        formatted_memory = {
                            "id": str(uuid.uuid4()),
                            "content": str(memory),
                            "metadata": {"agent_id": self.agent_id},
                            "timestamp": datetime.now().isoformat()
                        }
                    formatted_memories.append(formatted_memory)
                
                return formatted_memories
            else:
                print(f"❌ Failed to get all memories for {self.agent_id}: {result.get('error')}")
                return []
                
        except Exception as e:
            print(f"❌ Error getting all memories for agent {self.agent_id}: {str(e)}")
            return []
    
    async def delete_memory(self, memory_id: str) -> bool:
        """Delete a specific memory (Note: Mem0 doesn't support individual deletion)"""
        print(f"⚠️ Individual memory deletion not supported by Mem0 MCP. Memory ID: {memory_id}")
        return False
    
    async def clear_all_memories(self) -> bool:
        """Clear all memories for the agent"""
        try:
            await self.ensure_connected()
            
            result = await self.client.delete_all_memories(self.user_id)
            
            if result.get("success"):
                print(f"✅ Cleared all memories for {self.agent_id}")
                return True
            else:
                print(f"❌ Failed to clear memories for {self.agent_id}: {result.get('error')}")
                return False
                
        except Exception as e:
            print(f"❌ Error clearing memories for agent {self.agent_id}: {str(e)}")
            return False

class Mem0SharedMemoryManager:
    """Shared memory manager for cross-agent communication using Mem0"""
    
    def __init__(self):
        self.user_id = "shared_agent_memory"
        self.client = mem0_client
    
    async def ensure_connected(self):
        """Ensure connection to Mem0 MCP server"""
        if not self.client.connected:
            await self.client.connect()
    
    async def store_shared_memory(
        self,
        content: str,
        source_agent: str,
        target_agents: List[str] = None,
        metadata: Dict[str, Any] = None
    ) -> str:
        """Store shared memory accessible by multiple agents using Mem0"""
        try:
            await self.ensure_connected()
            
            # Enhance content with sharing context
            enhanced_content = f"[SHARED] {content}"
            enhanced_content += f" [Source: {source_agent}]"
            
            if target_agents:
                enhanced_content += f" [Targets: {', '.join(target_agents)}]"
            
            if metadata:
                context_info = []
                for key, value in metadata.items():
                    context_info.append(f"{key}: {value}")
                if context_info:
                    enhanced_content += f" [Context: {', '.join(context_info)}]"
            
            # Store in Mem0
            result = await self.client.add_memories([enhanced_content], self.user_id)
            
            if result.get("success"):
                memory_id = str(uuid.uuid4())
                print(f"✅ Stored shared memory from {source_agent}")
                return memory_id
            else:
                print(f"❌ Failed to store shared memory: {result.get('error')}")
                return ""
                
        except Exception as e:
            print(f"❌ Error storing shared memory: {str(e)}")
            return ""
    
    async def retrieve_shared_memories(
        self,
        query: str,
        requesting_agent: str,
        limit: int = 5
    ) -> List[Dict[str, Any]]:
        """Retrieve shared memories relevant to the query using Mem0"""
        try:
            await self.ensure_connected()
            
            # Search for shared memories
            result = await self.client.search_memory(query, self.user_id, limit)
            
            if result.get("success"):
                memories_data = result.get("data", {})
                memories = memories_data.get("memories", []) if isinstance(memories_data, dict) else []
                
                # Convert and filter memories
                formatted_memories = []
                for memory in memories:
                    if isinstance(memory, dict):
                        content = memory.get("memory", memory.get("content", ""))
                        
                        # Check if this is a shared memory and if requesting agent has access
                        if "[SHARED]" in content:
                            formatted_memory = {
                                "id": memory.get("id", str(uuid.uuid4())),
                                "content": content,
                                "metadata": {
                                    "shared": True,
                                    "requesting_agent": requesting_agent,
                                    "score": memory.get("score", 0.0)
                                },
                                "timestamp": memory.get("created_at", datetime.now().isoformat()),
                                "relevance_score": memory.get("score", 0.0)
                            }
                            formatted_memories.append(formatted_memory)
                
                print(f"✅ Retrieved {len(formatted_memories)} shared memories for {requesting_agent}")
                return formatted_memories
            else:
                print(f"❌ Failed to retrieve shared memories: {result.get('error')}")
                return []
                
        except Exception as e:
            print(f"❌ Error retrieving shared memories: {str(e)}")
            return []

# Tool functions for agents (maintaining compatibility with existing code)
async def store_agent_memory(agent_id: str, content: str, metadata: Dict[str, Any] = None) -> str:
    """Tool function to store memory for an agent using Mem0"""
    manager = Mem0AgentMemoryManager(agent_id)
    return await manager.store_memory(content, metadata)

async def retrieve_agent_memories(agent_id: str, query: str, limit: int = 5) -> List[Dict[str, Any]]:
    """Tool function to retrieve memories for an agent using Mem0"""
    manager = Mem0AgentMemoryManager(agent_id)
    return await manager.retrieve_memories(query, limit)

async def store_shared_memory(
    content: str,
    source_agent: str,
    target_agents: List[str] = None,
    metadata: Dict[str, Any] = None
) -> str:
    """Tool function to store shared memory using Mem0"""
    manager = Mem0SharedMemoryManager()
    return await manager.store_shared_memory(content, source_agent, target_agents, metadata)

async def retrieve_shared_memories(
    query: str,
    requesting_agent: str,
    limit: int = 5
) -> List[Dict[str, Any]]:
    """Tool function to retrieve shared memories using Mem0"""
    manager = Mem0SharedMemoryManager()
    return await manager.retrieve_shared_memories(query, requesting_agent, limit)

# Initialize connection on module import
async def initialize_mem0_connection():
    """Initialize connection to Mem0 MCP server"""
    await mem0_client.connect()

# Cleanup function
async def cleanup_mem0_connection():
    """Cleanup Mem0 MCP connection"""
    await mem0_client.disconnect()
