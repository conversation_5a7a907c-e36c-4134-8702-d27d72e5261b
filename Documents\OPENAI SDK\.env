# Multi-Agent System Environment Configuration

# Google Gemini API Configuration
GEMINI_API_KEY=AIzaSyBZWuoZTnFC5oNjbEV9BaBnodtEt3C7_Ns

# Gemini Model Configuration
GEMINI_MODEL=gemini-1.5-flash
GEMINI_TEMPERATURE=0.7
GEMINI_MAX_TOKENS=8192

# System Configuration
MAX_TURNS=20
TIMEOUT_SECONDS=300
DEBUG=false

# Memory System Configuration
USE_MEM0=true
MEMORY_PATH=./memory_db
MAX_MEMORIES_PER_AGENT=1000
MEMORY_RETENTION_DAYS=30

# Mem0 MCP Server Configuration
MEM0_MCP_URL=http://localhost:8762/mcp/openmemory/sse/Usama_Fresh
MEM0_TIMEOUT=30
MEM0_MAX_RETRIES=3

# Communication Configuration
MAX_COLLABORATION_DEPTH=3
ENABLE_INTER_AGENT_COMMUNICATION=true
MAX_COMMUNICATION_ROUNDS=5
COMMUNICATION_TIMEOUT=30
