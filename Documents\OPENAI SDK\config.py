"""
Configuration file for the Multi-Agent System
"""
import os
from dotenv import load_dotenv
from typing import Dict, Any

# Load environment variables
load_dotenv()

class Config:
    """Configuration class for the multi-agent system"""
    
    # API Keys
    GEMINI_API_KEY = os.getenv("GEMINI_API_KEY")
    MEM0_API_KEY = os.getenv("MEM0_API_KEY", "")  # Optional for local mem0
    OPENAI_API_KEY = os.getenv("OPENAI_API_KEY", "")  # For OpenAI SDK compatibility
    
    # Gemini Model Configuration
    GEMINI_MODEL = os.getenv("GEMINI_MODEL", "gemini-2.0-flash-exp")
    GEMINI_TEMPERATURE = float(os.getenv("GEMINI_TEMPERATURE", "0.7"))
    GEMINI_MAX_TOKENS = int(os.getenv("GEMINI_MAX_TOKENS", "8192"))
    
    # Agent Configuration
    MAX_TURNS = int(os.getenv("MAX_TURNS", "20"))
    TIMEOUT_SECONDS = int(os.getenv("TIMEOUT_SECONDS", "300"))
    
    # Memory Configuration
    MEM0_CONFIG = {
        "vector_store": {
            "provider": "chroma",
            "config": {
                "collection_name": "multi_agent_memory",
                "path": "./memory_db"
            }
        },
        "llm": {
            "provider": "gemini",
            "config": {
                "model": GEMINI_MODEL,
                "temperature": GEMINI_TEMPERATURE,
                "api_key": GEMINI_API_KEY
            }
        }
    }
    
    # Agent Roles and Descriptions
    AGENT_CONFIGS = {
        "healthcare": {
            "name": "Healthcare Research Agent",
            "description": "Specializes in medical research, health trends, clinical studies, and healthcare analysis",
            "expertise": ["medical research", "clinical trials", "health policy", "epidemiology", "pharmaceutical research"]
        },
        "ai_research": {
            "name": "AI Research Agent", 
            "description": "Focuses on AI/ML research, technology trends, algorithm analysis, and technical innovation",
            "expertise": ["machine learning", "deep learning", "AI ethics", "computer vision", "natural language processing"]
        },
        "coordinator": {
            "name": "Coordinator Agent",
            "description": "Orchestrates tasks between specialist agents and synthesizes comprehensive results",
            "expertise": ["task coordination", "result synthesis", "project management", "strategic planning"]
        }
    }
    
    @classmethod
    def validate_config(cls) -> bool:
        """Validate that required configuration is present"""
        if not cls.GEMINI_API_KEY:
            raise ValueError("GEMINI_API_KEY is required. Please set it in your environment variables.")
        return True
    
    @classmethod
    def get_agent_config(cls, agent_type: str) -> Dict[str, Any]:
        """Get configuration for a specific agent type"""
        return cls.AGENT_CONFIGS.get(agent_type, {})

# Validate configuration on import
Config.validate_config()
