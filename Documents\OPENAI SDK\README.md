# Multi-Agent Research System

A sophisticated multi-agent research system powered by Google Gemini with local memory management. Three specialized agents work together to provide comprehensive research and analysis across healthcare and AI domains.

## 🎯 Quick Start

### Prerequisites
- Python 3.8 or higher
- Google Gemini API key (already configured)

### Installation & Run
```bash
# 1. Install dependencies
pip install -r requirements.txt

# 2. Run the system
python main.py

# 3. Test configuration (optional)
python quick_test.py
```

## 🤖 Agent Capabilities

### 🏥 Healthcare Research Agent
- Medical literature search and analysis
- Clinical evidence evaluation
- Health trend analysis
- Drug information assessment
- Regulatory compliance analysis

### 🧠 AI Research Agent
- AI/ML paper search and analysis
- Technology trend identification
- Model performance evaluation
- Ethics and bias analysis
- Application research

### 🎯 Coordinator Agent
- Intelligent task routing
- Parallel task execution
- Result synthesis
- Cross-agent coordination
- Memory management

## 🔧 Key Features

✅ **Google Gemini Integration** - Advanced AI capabilities
✅ **Local Memory System** - Privacy-focused file storage
✅ **Smart Routing** - Automatic query classification
✅ **Parallel Processing** - Multiple agents work together
✅ **Web Search** - Real-time information retrieval
✅ **Result Synthesis** - Comprehensive multi-agent reports
✅ **No External Dependencies** - Fully self-contained

## 🎮 Usage Examples

### Interactive Mode
```bash
python main.py
```

Then type your questions:
- `What are the symptoms of diabetes?`
- `Explain machine learning algorithms`
- `AI applications in healthcare`
- `type:healthcare:Latest cancer treatments`
- `type:parallel:AI in medical diagnosis`

### Direct Queries
```bash
python main.py "What are AI applications in healthcare?"
python main.py "How does insulin work in the body?"
```

### Interactive Commands
| Command | Description |
|---------|-------------|
| `help` | Show available commands |
| `status` | Display system status |
| `quit` | Exit the system |
| `type:healthcare:[query]` | Route to healthcare agent |
| `type:ai_research:[query]` | Route to AI research agent |
| `type:parallel:[query]` | Use both agents |

## 🏗️ System Architecture

```
┌─────────────────────┐
│  Coordinator Agent  │ ← Central orchestrator
└─────────┬───────────┘
          │
    ┌─────┴─────┐
    │           │
┌───▼───┐   ┌───▼───┐
│Health │   │  AI   │ ← Specialist agents
│Agent  │   │Agent  │
└───────┘   └───────┘
    │           │
    └─────┬─────┘
          ▼
    ┌─────────┐
    │ Local   │ ← File-based memory
    │ Memory  │
    └─────────┘
```

## 📁 Project Structure

```
Documents\OPENAI SDK\
├── agents/                    # Agent implementations
│   ├── base_agent.py         # Base agent class
│   ├── coordinator_agent.py  # Main orchestrator
│   ├── healthcare_agent.py   # Medical specialist
│   └── ai_research_agent.py  # AI specialist
├── tools/                     # Tool implementations
│   ├── memory_tools.py       # Local memory system
│   └── research_tools.py     # Web search & analysis
├── utils/                     # Utilities
│   └── gemini_client.py      # Gemini API wrapper
├── prompts/                   # System prompts
│   ├── coordinator_prompt.md
│   ├── healthcare_prompt.md
│   └── ai_research_prompt.md
├── config.py                  # Configuration
├── main.py                    # Main script
├── quick_test.py             # Configuration test
├── requirements.txt          # Dependencies
├── .env                      # Environment (configured)
└── README.md                 # This file
```

## 💾 Memory System

### Local File Storage
- **Agent Memory**: Individual JSON files per agent
- **Shared Memory**: Cross-agent communication storage
- **Persistence**: Memory survives system restarts
- **Privacy**: All data stored locally

### Memory Features
- Simple text-based search
- Relevance scoring
- Automatic cleanup
- Configurable limits

## 🔍 Query Routing

The system automatically routes queries:

| Query Type | Example | Routing |
|------------|---------|---------|
| Healthcare | "diabetes symptoms" | Healthcare Agent |
| AI/Tech | "machine learning" | AI Research Agent |
| Cross-domain | "AI in medicine" | Both Agents (Parallel) |
| General | "research methods" | Coordinator |

## 🛠️ Troubleshooting

### Common Issues

1. **Import Errors**
   ```
   ModuleNotFoundError: No module named 'agents'
   ```
   **Solution**: Run from project root directory

2. **Memory Errors**
   ```
   Error initializing memory
   ```
   **Solution**: Check memory path permissions and disk space

3. **API Connection Issues**
   ```
   Error connecting to Gemini API
   ```
   **Solution**: Check your internet connection

### Debug Mode
Add to `.env` file:
```env
DEBUG=true
```

## 🔒 Privacy & Security

- **Local Storage**: All memory data stays on your machine
- **No Cloud Dependencies**: No external memory services
- **API Key Security**: Embedded securely in configuration
- **Full Control**: You own all your data

## 🚀 Getting Started

1. **Install dependencies**: `pip install -r requirements.txt`
2. **Run the system**: `python main.py`
3. **Test configuration**: `python quick_test.py`
4. **Start asking questions!**

## 🎯 Example Queries

### Healthcare
- "What are the symptoms of Type 2 diabetes?"
- "Latest treatments for cancer"
- "How do vaccines work?"

### AI Research
- "Explain transformer neural networks"
- "Recent advances in computer vision"
- "What is reinforcement learning?"

### Cross-Domain
- "AI applications in medical diagnosis"
- "Machine learning for drug discovery"
- "Ethical considerations of AI in medicine"

---

**Ready to start?** Run `python main.py` and begin exploring! 🚀
