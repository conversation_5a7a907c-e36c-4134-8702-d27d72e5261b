# Multi-Agent Communication System

A sophisticated multi-agent system where specialized agents communicate and collaborate in real-time. Powered by Google Gemini, three agents work together across healthcare and AI domains with advanced inter-agent communication.

## 🚀 Quick Start

```bash
# Install dependencies
pip install -r requirements.txt

# Test Mem0 MCP integration (optional)
python test_mem0_integration.py

# Run the system
python main.py
```

## 🤖 Agent Capabilities

### 🏥 Healthcare Research Agent
- Medical literature search and analysis
- Clinical evidence evaluation
- Health trend analysis
- Drug information assessment
- Regulatory compliance analysis

### 🧠 AI Research Agent
- AI/ML paper search and analysis
- Technology trend identification
- Model performance evaluation
- Ethics and bias analysis
- Application research

### 🎯 Coordinator Agent
- Intelligent task routing
- Parallel task execution
- Result synthesis
- Cross-agent coordination
- Memory management

## 🔗 Communication & Memory Features

✅ **Real-Time Agent Communication** - Agents message each other during query processing
✅ **Automatic Collaboration** - Agents detect when they need help from other domains
✅ **Cross-Domain Expertise** - Healthcare and AI agents share knowledge
✅ **Intelligent Routing** - Queries automatically routed to appropriate agents
✅ **Google Gemini Powered** - Advanced AI capabilities with your API key
✅ **Mem0 MCP Integration** - Advanced semantic memory with your running Mem0 server
✅ **Persistent Memory** - Sophisticated memory storage and retrieval across sessions

## 🎮 Usage Examples

### Interactive Mode
```bash
python main.py
```

Then type your questions:
- `What are the symptoms of diabetes?`
- `Explain machine learning algorithms`
- `AI applications in healthcare`
- `type:healthcare:Latest cancer treatments`
- `type:parallel:AI in medical diagnosis`

### Direct Queries
```bash
python main.py "What are AI applications in healthcare?"
python main.py "How does insulin work in the body?"
```

### Interactive Commands
| Command | Description |
|---------|-------------|
| `help` | Show available commands |
| `status` | Display system status |
| `quit` | Exit the system |
| `type:healthcare:[query]` | Route to healthcare agent |
| `type:ai_research:[query]` | Route to AI research agent |
| `type:parallel:[query]` | Use both agents |

## 🏗️ System Architecture

```
┌─────────────────────┐    ┌─────────────────────┐    ┌─────────────────────┐
│  Healthcare Agent   │◄──►│   AI Research Agent │◄──►│  Coordinator Agent  │
└─────────┬───────────┘    └─────────┬───────────┘    └─────────┬───────────┘
          │                          │                          │
          └──────────────────────────┼──────────────────────────┘
                                     │
                        ┌─────────────▼─────────────┐
                        │   Communication Hub       │
                        │ • Message Routing         │
                        │ • Collaboration Requests  │
                        │ • Real-time Communication │
                        └───────────────────────────┘
```

## 🧠 Mem0 MCP Integration

The system integrates with your running Mem0 MCP server for advanced memory capabilities:

### Memory Features
- **Semantic Search** - Advanced AI-powered memory retrieval
- **Persistent Storage** - Memories survive system restarts
- **Cross-Agent Sharing** - Agents can access each other's relevant memories
- **Real-Time Updates** - Memory updates during conversations
- **Context Awareness** - Memories include rich metadata and context

### Mem0 Server Configuration
- **Server URL**: `http://localhost:8762/mcp/openmemory/sse/Usama_Fresh`
- **Available Tools**: `add_memories`, `search_memory`, `list_memories`, `delete_all_memories`
- **Auto-Connection**: System connects automatically on startup

## 🎯 Communication Examples

### Cross-Domain Query with Memory
**Query**: "How can machine learning improve medical diagnosis?"
- Healthcare Agent receives query
- Searches Mem0 for relevant medical knowledge
- Detects need for AI expertise
- Requests collaboration from AI Research Agent
- AI Agent searches Mem0 for ML insights
- Both agents store collaboration insights in Mem0
- Returns comprehensive answer with persistent memory

### Memory-Enhanced Coordination
**Query**: "Compare traditional and AI approaches to drug discovery"
- Coordinator searches Mem0 for previous research
- Requests input from both specialist agents
- Agents contribute domain-specific memories
- Results stored in shared Mem0 memory
- Synthesizes comprehensive comparison

## 🎯 Example Communication Scenarios

### Cross-Domain Query
**"How can machine learning improve medical diagnosis?"**
- Healthcare Agent receives query
- Detects need for AI expertise
- Requests collaboration from AI Research Agent
- Combines medical knowledge with AI insights

### Coordination Query
**"Compare traditional and AI approaches to drug discovery"**
- Coordinator orchestrates collaboration
- Requests input from both specialist agents
- Synthesizes comprehensive comparison

---

**Ready to start?** Run `python main.py` and begin exploring! 🚀
