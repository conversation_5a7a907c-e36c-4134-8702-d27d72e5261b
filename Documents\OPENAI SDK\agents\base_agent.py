"""
Base agent class for the multi-agent system with Gemini integration
"""
import asyncio
from typing import Dict, List, Any, Optional, Callable
from abc import ABC, abstractmethod
import json
from datetime import datetime
import sys
import os

# Add the parent directory to the path to import modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.gemini_client import gemini_client
from tools.memory_tools import AgentMemoryManager, store_agent_memory, retrieve_agent_memories
from tools.research_tools import web_search, fetch_webpage, analyze_sentiment, extract_topics, summarize_research
from config import Config

class BaseAgent(ABC):
    """Base class for all agents in the multi-agent system"""
    
    def __init__(
        self, 
        agent_id: str, 
        name: str, 
        description: str,
        system_prompt: str,
        tools: List[Callable] = None
    ):
        self.agent_id = agent_id
        self.name = name
        self.description = description
        self.system_prompt = system_prompt
        self.memory_manager = AgentMemoryManager(agent_id)
        
        # Initialize tools
        self.tools = tools or []
        self._setup_default_tools()
        
        # Conversation history
        self.conversation_history = []
        
        # Agent state
        self.is_active = False
        self.current_task = None
        
    def _setup_default_tools(self):
        """Setup default tools available to all agents"""
        default_tools = [
            {
                "name": "web_search",
                "description": "Search the web for information on a given query",
                "function": web_search,
                "parameters": {
                    "type": "object",
                    "properties": {
                        "query": {"type": "string", "description": "Search query"},
                        "num_results": {"type": "integer", "description": "Number of results to return", "default": 5}
                    },
                    "required": ["query"]
                }
            },
            {
                "name": "fetch_webpage",
                "description": "Fetch content from a specific webpage URL",
                "function": fetch_webpage,
                "parameters": {
                    "type": "object",
                    "properties": {
                        "url": {"type": "string", "description": "URL to fetch content from"}
                    },
                    "required": ["url"]
                }
            },
            {
                "name": "analyze_sentiment",
                "description": "Analyze the sentiment of given text",
                "function": analyze_sentiment,
                "parameters": {
                    "type": "object",
                    "properties": {
                        "text": {"type": "string", "description": "Text to analyze"}
                    },
                    "required": ["text"]
                }
            },
            {
                "name": "store_memory",
                "description": "Store information in agent memory",
                "function": self._store_memory_wrapper,
                "parameters": {
                    "type": "object",
                    "properties": {
                        "content": {"type": "string", "description": "Content to store"},
                        "metadata": {"type": "object", "description": "Additional metadata"}
                    },
                    "required": ["content"]
                }
            },
            {
                "name": "retrieve_memories",
                "description": "Retrieve relevant memories based on a query",
                "function": self._retrieve_memories_wrapper,
                "parameters": {
                    "type": "object",
                    "properties": {
                        "query": {"type": "string", "description": "Query to search memories"},
                        "limit": {"type": "integer", "description": "Maximum number of memories to retrieve", "default": 5}
                    },
                    "required": ["query"]
                }
            }
        ]
        
        self.tools.extend(default_tools)
    
    async def _store_memory_wrapper(self, content: str, metadata: Dict[str, Any] = None) -> str:
        """Wrapper for storing memory"""
        return await store_agent_memory(self.agent_id, content, metadata)
    
    async def _retrieve_memories_wrapper(self, query: str, limit: int = 5) -> List[Dict[str, Any]]:
        """Wrapper for retrieving memories"""
        return await retrieve_agent_memories(self.agent_id, query, limit)
    
    async def process_message(self, message: str, context: Dict[str, Any] = None) -> str:
        """Process a message and generate a response"""
        try:
            self.is_active = True
            self.current_task = message
            
            # Add message to conversation history
            self.conversation_history.append({
                "role": "user",
                "content": message,
                "timestamp": datetime.now().isoformat(),
                "context": context or {}
            })
            
            # Retrieve relevant memories for context
            relevant_memories = await self._retrieve_memories_wrapper(message, limit=3)
            memory_context = self._format_memory_context(relevant_memories)
            
            # Prepare messages for Gemini
            messages = self._prepare_messages(message, memory_context, context)
            
            # Generate response using Gemini
            response = await self._generate_response(messages)
            
            # Store the interaction in memory
            await self._store_interaction(message, response, context)
            
            # Add response to conversation history
            self.conversation_history.append({
                "role": "assistant",
                "content": response,
                "timestamp": datetime.now().isoformat()
            })
            
            return response
            
        except Exception as e:
            error_msg = f"Error processing message: {str(e)}"
            print(f"Agent {self.agent_id} error: {error_msg}")
            return error_msg
        finally:
            self.is_active = False
            self.current_task = None
    
    def _prepare_messages(self, message: str, memory_context: str, context: Dict[str, Any] = None) -> List[Dict[str, str]]:
        """Prepare messages for the LLM"""
        messages = [
            {"role": "system", "content": self.system_prompt}
        ]
        
        # Add memory context if available
        if memory_context:
            messages.append({
                "role": "system", 
                "content": f"Relevant context from memory:\n{memory_context}"
            })
        
        # Add additional context if provided
        if context:
            context_str = json.dumps(context, indent=2)
            messages.append({
                "role": "system",
                "content": f"Additional context:\n{context_str}"
            })
        
        # Add recent conversation history (last 5 messages)
        recent_history = self.conversation_history[-5:]
        for hist_msg in recent_history:
            messages.append({
                "role": hist_msg["role"],
                "content": hist_msg["content"]
            })
        
        # Add current message
        messages.append({"role": "user", "content": message})
        
        return messages
    
    async def _generate_response(self, messages: List[Dict[str, str]]) -> str:
        """Generate response using Gemini"""
        try:
            # Convert tools to OpenAI format for compatibility
            openai_tools = self._convert_tools_to_openai_format()
            
            response = await gemini_client.create_completion(
                messages=messages,
                temperature=Config.GEMINI_TEMPERATURE,
                max_tokens=Config.GEMINI_MAX_TOKENS,
                tools=openai_tools if openai_tools else None
            )
            
            if response and "choices" in response and len(response["choices"]) > 0:
                return response["choices"][0]["message"]["content"]
            else:
                return "I apologize, but I couldn't generate a proper response. Please try again."
                
        except Exception as e:
            return f"Error generating response: {str(e)}"
    
    def _convert_tools_to_openai_format(self) -> List[Dict[str, Any]]:
        """Convert tools to OpenAI format"""
        openai_tools = []
        for tool in self.tools:
            if isinstance(tool, dict):
                openai_tool = {
                    "type": "function",
                    "function": {
                        "name": tool["name"],
                        "description": tool["description"],
                        "parameters": tool["parameters"]
                    }
                }
                openai_tools.append(openai_tool)
        return openai_tools
    
    def _format_memory_context(self, memories: List[Dict[str, Any]]) -> str:
        """Format memories into context string"""
        if not memories:
            return ""
        
        context_parts = []
        for memory in memories:
            content = memory.get("memory", memory.get("content", ""))
            if content:
                context_parts.append(f"- {content}")
        
        return "\n".join(context_parts) if context_parts else ""
    
    async def _store_interaction(self, message: str, response: str, context: Dict[str, Any] = None):
        """Store the interaction in memory"""
        interaction_content = f"User: {message}\nAgent Response: {response}"
        metadata = {
            "interaction_type": "conversation",
            "timestamp": datetime.now().isoformat(),
            "context": context or {}
        }
        await self._store_memory_wrapper(interaction_content, metadata)
    
    def get_status(self) -> Dict[str, Any]:
        """Get current agent status"""
        return {
            "agent_id": self.agent_id,
            "name": self.name,
            "is_active": self.is_active,
            "current_task": self.current_task,
            "conversation_length": len(self.conversation_history),
            "available_tools": len(self.tools)
        }
    
    @abstractmethod
    async def specialized_task(self, task: str, **kwargs) -> Dict[str, Any]:
        """Abstract method for specialized tasks - to be implemented by subclasses"""
        pass
