"""
Base agent class for the multi-agent system with Gemini integration
"""
import asyncio
from typing import Dict, List, Any, Optional, Callable
from abc import ABC, abstractmethod
import json
from datetime import datetime
import sys
import os

# Add the parent directory to the path to import modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.gemini_client import gemini_client
from tools.research_tools import web_search, fetch_webpage, analyze_sentiment, extract_topics, summarize_research
from tools.communication_tools import (
    communication_hub, send_message_to_agent, request_agent_help,
    broadcast_to_agents, get_agent_messages, MessageType, MessagePriority, AgentMessage
)
from config import Config

# Import memory tools based on configuration
if Config.MEMORY_CONFIG.get("use_mem0", True):
    from tools.mem0_mcp_tools import (
        Mem0AgentMemoryManager as AgentMemoryManager,
        store_agent_memory, retrieve_agent_memories,
        store_shared_memory, retrieve_shared_memories,
        initialize_mem0_connection
    )
    print("🔗 Using Mem0 MCP for memory management")
else:
    from tools.memory_tools import (
        AgentMemoryManager, store_agent_memory, retrieve_agent_memories,
        store_shared_memory, retrieve_shared_memories
    )
    print("📁 Using local file-based memory management")

class BaseAgent(ABC):
    """Base class for all agents in the multi-agent system"""
    
    def __init__(
        self,
        agent_id: str,
        name: str,
        description: str,
        system_prompt: str,
        tools: List[Callable] = None
    ):
        self.agent_id = agent_id
        self.name = name
        self.description = description
        self.system_prompt = system_prompt
        self.memory_manager = AgentMemoryManager(agent_id)

        # Initialize tools
        self.tools = tools or []
        self._setup_default_tools()
        self._setup_communication_tools()

        # Conversation history
        self.conversation_history = []

        # Agent state
        self.is_active = False
        self.current_task = None
        self.collaboration_depth = 0

        # Communication state
        self.pending_collaborations = {}
        self.active_conversations = {}

        # Register with communication hub
        communication_hub.register_agent(self.agent_id, self)
        
    def _setup_default_tools(self):
        """Setup default tools available to all agents"""
        default_tools = [
            {
                "name": "web_search",
                "description": "Search the web for information on a given query",
                "function": web_search,
                "parameters": {
                    "type": "object",
                    "properties": {
                        "query": {"type": "string", "description": "Search query"},
                        "num_results": {"type": "integer", "description": "Number of results to return", "default": 5}
                    },
                    "required": ["query"]
                }
            },
            {
                "name": "fetch_webpage",
                "description": "Fetch content from a specific webpage URL",
                "function": fetch_webpage,
                "parameters": {
                    "type": "object",
                    "properties": {
                        "url": {"type": "string", "description": "URL to fetch content from"}
                    },
                    "required": ["url"]
                }
            },
            {
                "name": "analyze_sentiment",
                "description": "Analyze the sentiment of given text",
                "function": analyze_sentiment,
                "parameters": {
                    "type": "object",
                    "properties": {
                        "text": {"type": "string", "description": "Text to analyze"}
                    },
                    "required": ["text"]
                }
            },
            {
                "name": "store_memory",
                "description": "Store information in agent memory",
                "function": self._store_memory_wrapper,
                "parameters": {
                    "type": "object",
                    "properties": {
                        "content": {"type": "string", "description": "Content to store"},
                        "metadata": {"type": "object", "description": "Additional metadata"}
                    },
                    "required": ["content"]
                }
            },
            {
                "name": "retrieve_memories",
                "description": "Retrieve relevant memories based on a query",
                "function": self._retrieve_memories_wrapper,
                "parameters": {
                    "type": "object",
                    "properties": {
                        "query": {"type": "string", "description": "Query to search memories"},
                        "limit": {"type": "integer", "description": "Maximum number of memories to retrieve", "default": 5}
                    },
                    "required": ["query"]
                }
            }
        ]
        
        self.tools.extend(default_tools)

    def _setup_communication_tools(self):
        """Setup communication tools for inter-agent collaboration"""
        communication_tools = [
            {
                "name": "send_message_to_agent",
                "description": "Send a message to another agent",
                "function": self._send_message_to_agent,
                "parameters": {
                    "type": "object",
                    "properties": {
                        "recipient_id": {"type": "string", "description": "ID of the recipient agent"},
                        "content": {"type": "object", "description": "Message content"},
                        "message_type": {"type": "string", "description": "Type of message (request, notification, etc.)"},
                        "requires_response": {"type": "boolean", "description": "Whether response is required"}
                    },
                    "required": ["recipient_id", "content"]
                }
            },
            {
                "name": "request_collaboration",
                "description": "Request collaboration from another agent for a specific task",
                "function": self._request_collaboration,
                "parameters": {
                    "type": "object",
                    "properties": {
                        "target_agent": {"type": "string", "description": "Agent to collaborate with"},
                        "task_description": {"type": "string", "description": "Description of the task needing collaboration"},
                        "context": {"type": "object", "description": "Additional context for the collaboration"}
                    },
                    "required": ["target_agent", "task_description"]
                }
            },
            {
                "name": "broadcast_message",
                "description": "Broadcast a message to all other agents",
                "function": self._broadcast_message,
                "parameters": {
                    "type": "object",
                    "properties": {
                        "content": {"type": "object", "description": "Message content to broadcast"},
                        "exclude_agents": {"type": "array", "description": "List of agent IDs to exclude from broadcast"}
                    },
                    "required": ["content"]
                }
            },
            {
                "name": "check_messages",
                "description": "Check for incoming messages from other agents",
                "function": self._check_messages,
                "parameters": {
                    "type": "object",
                    "properties": {
                        "message_type": {"type": "string", "description": "Filter by message type (optional)"}
                    }
                }
            },
            {
                "name": "get_agent_capabilities",
                "description": "Get capabilities of other agents in the system",
                "function": self._get_other_agent_capabilities,
                "parameters": {
                    "type": "object",
                    "properties": {
                        "agent_id": {"type": "string", "description": "Specific agent ID (optional)"}
                    }
                }
            }
        ]

        self.tools.extend(communication_tools)
    
    async def _store_memory_wrapper(self, content: str, metadata: Dict[str, Any] = None) -> str:
        """Wrapper for storing memory"""
        return await store_agent_memory(self.agent_id, content, metadata)
    
    async def _retrieve_memories_wrapper(self, query: str, limit: int = 5) -> List[Dict[str, Any]]:
        """Wrapper for retrieving memories"""
        return await retrieve_agent_memories(self.agent_id, query, limit)

    # Communication tool implementations
    async def _send_message_to_agent(
        self,
        recipient_id: str,
        content: Dict[str, Any],
        message_type: str = "request",
        requires_response: bool = False
    ) -> Dict[str, Any]:
        """Send a message to another agent"""
        try:
            msg_type = MessageType(message_type) if message_type in [t.value for t in MessageType] else MessageType.REQUEST

            success = await send_message_to_agent(
                sender_id=self.agent_id,
                recipient_id=recipient_id,
                content=content,
                message_type=msg_type,
                priority=MessagePriority.NORMAL,
                requires_response=requires_response
            )

            return {
                "success": success,
                "message": f"Message sent to {recipient_id}" if success else f"Failed to send message to {recipient_id}"
            }
        except Exception as e:
            return {"success": False, "error": str(e)}

    async def _request_collaboration(
        self,
        target_agent: str,
        task_description: str,
        context: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Request collaboration from another agent"""
        try:
            if self.collaboration_depth >= Config.MAX_COLLABORATION_DEPTH:
                return {
                    "success": False,
                    "error": "Maximum collaboration depth reached"
                }

            collaboration_id = await request_agent_help(
                requester_id=self.agent_id,
                target_agent_id=target_agent,
                task_description=task_description,
                context=context or {}
            )

            if collaboration_id:
                self.pending_collaborations[collaboration_id] = {
                    "target_agent": target_agent,
                    "task_description": task_description,
                    "timestamp": datetime.now().isoformat()
                }

                return {
                    "success": True,
                    "collaboration_id": collaboration_id,
                    "message": f"Collaboration request sent to {target_agent}"
                }
            else:
                return {
                    "success": False,
                    "error": f"Failed to send collaboration request to {target_agent}"
                }
        except Exception as e:
            return {"success": False, "error": str(e)}

    async def _broadcast_message(
        self,
        content: Dict[str, Any],
        exclude_agents: List[str] = None
    ) -> Dict[str, Any]:
        """Broadcast a message to all other agents"""
        try:
            sent_count = await broadcast_to_agents(
                sender_id=self.agent_id,
                content=content,
                exclude_agents=exclude_agents or []
            )

            return {
                "success": True,
                "sent_to_count": sent_count,
                "message": f"Broadcast sent to {sent_count} agents"
            }
        except Exception as e:
            return {"success": False, "error": str(e)}

    async def _check_messages(self, message_type: str = None) -> Dict[str, Any]:
        """Check for incoming messages from other agents"""
        try:
            msg_type = None
            if message_type and message_type in [t.value for t in MessageType]:
                msg_type = MessageType(message_type)

            messages = await get_agent_messages(self.agent_id, msg_type)

            # Process messages
            processed_messages = []
            for message in messages:
                processed_msg = {
                    "id": message.id,
                    "sender": message.sender_id,
                    "type": message.message_type.value,
                    "priority": message.priority.value,
                    "content": message.content,
                    "timestamp": message.timestamp,
                    "requires_response": message.requires_response
                }
                processed_messages.append(processed_msg)

                # Handle collaboration requests automatically
                if message.message_type == MessageType.COLLABORATION_REQUEST:
                    await self._handle_collaboration_request(message)

            return {
                "success": True,
                "message_count": len(processed_messages),
                "messages": processed_messages
            }
        except Exception as e:
            return {"success": False, "error": str(e)}

    async def _get_other_agent_capabilities(self, agent_id: str = None) -> Dict[str, Any]:
        """Get capabilities of other agents in the system"""
        try:
            agent_status = communication_hub.get_agent_status()

            if agent_id:
                if agent_id in agent_status:
                    return {
                        "success": True,
                        "agent_capabilities": {agent_id: agent_status[agent_id]}
                    }
                else:
                    return {
                        "success": False,
                        "error": f"Agent {agent_id} not found"
                    }
            else:
                # Remove self from the list
                other_agents = {k: v for k, v in agent_status.items() if k != self.agent_id}
                return {
                    "success": True,
                    "all_agent_capabilities": other_agents
                }
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    async def _handle_collaboration_request(self, message: AgentMessage) -> None:
        """Handle incoming collaboration requests"""
        try:
            collaboration_id = message.content.get("collaboration_id")
            task_description = message.content.get("task_description")
            context = message.content.get("context", {})

            # Check if this agent can help with the task
            can_help = await self._can_help_with_task(task_description, context)

            if can_help:
                # Process the collaboration request
                response_content = await self._process_collaboration_task(task_description, context)

                # Send response back
                await communication_hub.respond_to_collaboration(
                    responder_id=self.agent_id,
                    collaboration_id=collaboration_id,
                    response_content=response_content,
                    original_message_id=message.id
                )

                print(f"🤝 {self.agent_id} provided collaboration response for: {task_description[:50]}...")
            else:
                # Send a polite decline
                decline_response = {
                    "can_help": False,
                    "reason": f"Task outside {self.agent_id} expertise area",
                    "suggested_agents": self._suggest_alternative_agents(task_description)
                }

                await communication_hub.respond_to_collaboration(
                    responder_id=self.agent_id,
                    collaboration_id=collaboration_id,
                    response_content=decline_response,
                    original_message_id=message.id
                )

        except Exception as e:
            print(f"❌ Error handling collaboration request: {str(e)}")

    async def process_message(self, message: str, context: Dict[str, Any] = None) -> str:
        """Process a message and generate a response with inter-agent communication"""
        try:
            self.is_active = True
            self.current_task = message

            # Check for incoming messages first
            await self._check_messages()

            # Add message to conversation history
            self.conversation_history.append({
                "role": "user",
                "content": message,
                "timestamp": datetime.now().isoformat(),
                "context": context or {}
            })

            # Retrieve relevant memories for context
            relevant_memories = await self._retrieve_memories_wrapper(message, limit=3)
            memory_context = self._format_memory_context(relevant_memories)

            # Check if this task might benefit from collaboration
            collaboration_needed = await self._assess_collaboration_need(message, context)

            # Prepare messages for Gemini (including collaboration context)
            messages = self._prepare_messages(message, memory_context, context, collaboration_needed)

            # Generate response using Gemini
            response = await self._generate_response(messages)

            # Store the interaction in memory
            await self._store_interaction(message, response, context)

            # Add response to conversation history
            self.conversation_history.append({
                "role": "assistant",
                "content": response,
                "timestamp": datetime.now().isoformat()
            })

            return response

        except Exception as e:
            error_msg = f"Error processing message: {str(e)}"
            print(f"Agent {self.agent_id} error: {error_msg}")
            return error_msg
        finally:
            self.is_active = False
            self.current_task = None
    
    def _prepare_messages(self, message: str, memory_context: str, context: Dict[str, Any] = None, collaboration_needed: Dict[str, Any] = None) -> List[Dict[str, str]]:
        """Prepare messages for the LLM"""
        messages = [
            {"role": "system", "content": self.system_prompt}
        ]
        
        # Add memory context if available
        if memory_context:
            messages.append({
                "role": "system", 
                "content": f"Relevant context from memory:\n{memory_context}"
            })
        
        # Add additional context if provided
        if context:
            context_str = json.dumps(context, indent=2)
            messages.append({
                "role": "system",
                "content": f"Additional context:\n{context_str}"
            })

        # Add collaboration context if needed
        if collaboration_needed and collaboration_needed.get("needed", False):
            collab_context = f"""
Collaboration Assessment:
- Other agents available: {', '.join(collaboration_needed.get('available_agents', []))}
- Collaboration recommended for: {collaboration_needed.get('reason', 'Complex multi-domain task')}
- You can use the 'request_collaboration' tool to get help from other agents
- You can use 'get_agent_capabilities' to see what other agents can help with
"""
            messages.append({
                "role": "system",
                "content": collab_context
            })
        
        # Add recent conversation history (last 5 messages)
        recent_history = self.conversation_history[-5:]
        for hist_msg in recent_history:
            messages.append({
                "role": hist_msg["role"],
                "content": hist_msg["content"]
            })
        
        # Add current message
        messages.append({"role": "user", "content": message})
        
        return messages
    
    async def _generate_response(self, messages: List[Dict[str, str]]) -> str:
        """Generate response using Gemini"""
        try:
            # Convert tools to OpenAI format for compatibility
            openai_tools = self._convert_tools_to_openai_format()
            
            response = await gemini_client.create_completion(
                messages=messages,
                temperature=Config.GEMINI_TEMPERATURE,
                max_tokens=Config.GEMINI_MAX_TOKENS,
                tools=openai_tools if openai_tools else None
            )
            
            if response and "choices" in response and len(response["choices"]) > 0:
                return response["choices"][0]["message"]["content"]
            else:
                return "I apologize, but I couldn't generate a proper response. Please try again."
                
        except Exception as e:
            return f"Error generating response: {str(e)}"
    
    def _convert_tools_to_openai_format(self) -> List[Dict[str, Any]]:
        """Convert tools to OpenAI format"""
        openai_tools = []
        for tool in self.tools:
            if isinstance(tool, dict):
                openai_tool = {
                    "type": "function",
                    "function": {
                        "name": tool["name"],
                        "description": tool["description"],
                        "parameters": tool["parameters"]
                    }
                }
                openai_tools.append(openai_tool)
        return openai_tools
    
    def _format_memory_context(self, memories: List[Dict[str, Any]]) -> str:
        """Format memories into context string"""
        if not memories:
            return ""
        
        context_parts = []
        for memory in memories:
            content = memory.get("memory", memory.get("content", ""))
            if content:
                context_parts.append(f"- {content}")
        
        return "\n".join(context_parts) if context_parts else ""
    
    async def _store_interaction(self, message: str, response: str, context: Dict[str, Any] = None):
        """Store the interaction in memory"""
        interaction_content = f"User: {message}\nAgent Response: {response}"
        metadata = {
            "interaction_type": "conversation",
            "timestamp": datetime.now().isoformat(),
            "context": context or {}
        }
        await self._store_memory_wrapper(interaction_content, metadata)
    
    def get_status(self) -> Dict[str, Any]:
        """Get current agent status"""
        return {
            "agent_id": self.agent_id,
            "name": self.name,
            "is_active": self.is_active,
            "current_task": self.current_task,
            "conversation_length": len(self.conversation_history),
            "available_tools": len(self.tools)
        }
    
    # Helper methods for collaboration
    async def _assess_collaboration_need(self, message: str, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """Assess if the current task would benefit from collaboration"""
        # Get available agents
        agent_status = communication_hub.get_agent_status()
        available_agents = [agent_id for agent_id in agent_status.keys() if agent_id != self.agent_id]

        # Simple heuristic: if message contains keywords from other domains, suggest collaboration
        message_lower = message.lower()

        # Check for cross-domain indicators
        cross_domain_keywords = {
            "healthcare": ["medical", "health", "clinical", "patient", "treatment", "diagnosis"],
            "ai_research": ["ai", "machine learning", "algorithm", "model", "neural", "artificial intelligence"],
            "data_analysis": ["data", "analysis", "statistics", "visualization", "trends"]
        }

        current_domain = self.agent_id.replace("_agent", "")
        other_domains = [domain for domain in cross_domain_keywords.keys() if domain != current_domain]

        collaboration_needed = False
        relevant_agents = []

        for domain in other_domains:
            if any(keyword in message_lower for keyword in cross_domain_keywords[domain]):
                collaboration_needed = True
                relevant_agents.append(f"{domain}_agent")

        return {
            "needed": collaboration_needed,
            "available_agents": available_agents,
            "relevant_agents": relevant_agents,
            "reason": "Cross-domain expertise detected" if collaboration_needed else "Single domain task"
        }

    async def _can_help_with_task(self, task_description: str, context: Dict[str, Any]) -> bool:
        """Determine if this agent can help with a given task"""
        # Get agent's expertise areas
        agent_config = Config.get_agent_config(self.agent_id)
        expertise = agent_config.get("expertise", []) if agent_config else []

        task_lower = task_description.lower()

        # Check if task matches agent's expertise
        for skill in expertise:
            if skill.lower() in task_lower:
                return True

        return False

    async def _process_collaboration_task(self, task_description: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Process a collaboration task and return results"""
        try:
            # Increase collaboration depth
            self.collaboration_depth += 1

            # Process the task using the agent's specialized capabilities
            response = await self.process_message(task_description, context)

            return {
                "can_help": True,
                "response": response,
                "agent_id": self.agent_id,
                "expertise_used": Config.get_agent_config(self.agent_id).get("expertise", []),
                "confidence": 0.8  # Could be calculated based on task match
            }
        except Exception as e:
            return {
                "can_help": False,
                "error": str(e),
                "agent_id": self.agent_id
            }
        finally:
            self.collaboration_depth = max(0, self.collaboration_depth - 1)

    def _suggest_alternative_agents(self, task_description: str) -> List[str]:
        """Suggest alternative agents that might be better suited for the task"""
        task_lower = task_description.lower()
        suggestions = []

        # Simple keyword-based suggestions
        if any(word in task_lower for word in ["medical", "health", "clinical", "patient"]):
            suggestions.append("healthcare_agent")

        if any(word in task_lower for word in ["ai", "machine learning", "algorithm", "model"]):
            suggestions.append("ai_research_agent")

        if any(word in task_lower for word in ["coordinate", "manage", "organize", "plan"]):
            suggestions.append("coordinator_agent")

        return suggestions

    @abstractmethod
    async def specialized_task(self, task: str, **kwargs) -> Dict[str, Any]:
        """Abstract method for specialized tasks - to be implemented by subclasses"""
        pass
