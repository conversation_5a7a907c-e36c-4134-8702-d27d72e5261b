"""
Demonstration script for Multi-Agent Communication System
Shows how agents communicate and collaborate
"""
import asyncio
import sys
import os

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from main import MultiAgentSystem

async def demo_communication():
    """Demonstrate the multi-agent communication capabilities"""
    print("🎬 Multi-Agent Communication System Demo")
    print("=" * 50)
    print("This demo shows how agents communicate and collaborate when handling queries.\n")
    
    # Initialize the system
    system = MultiAgentSystem()
    await system.initialize()
    
    print("\n" + "="*50)
    print("🎯 DEMO 1: Cross-Domain Query (Healthcare + AI)")
    print("="*50)
    print("Query: 'How can machine learning improve medical diagnosis accuracy?'")
    print("Expected: Healthcare agent may request collaboration from AI research agent\n")
    
    result1 = await system.process_query(
        "How can machine learning improve medical diagnosis accuracy?",
        "auto"
    )
    
    print(f"\n📋 Result Preview: {str(result1['result'])[:200]}...")
    
    print("\n" + "="*50)
    print("🎯 DEMO 2: AI Research Query with Healthcare Context")
    print("="*50)
    print("Query: 'What are the latest AI models for analyzing medical images?'")
    print("Expected: AI research agent may request healthcare domain knowledge\n")
    
    result2 = await system.process_query(
        "What are the latest AI models for analyzing medical images?",
        "auto"
    )
    
    print(f"\n📋 Result Preview: {str(result2['result'])[:200]}...")
    
    print("\n" + "="*50)
    print("🎯 DEMO 3: Coordination Query")
    print("="*50)
    print("Query: 'Compare traditional and AI-based approaches to drug discovery'")
    print("Expected: Coordinator orchestrates collaboration between both specialist agents\n")
    
    result3 = await system.process_query(
        "Compare traditional and AI-based approaches to drug discovery",
        "coordination"
    )
    
    print(f"\n📋 Result Preview: {str(result3['result'])[:200]}...")
    
    print("\n" + "="*50)
    print("🎯 DEMO 4: Healthcare Query (Single Domain)")
    print("="*50)
    print("Query: 'What are the symptoms of Type 2 diabetes?'")
    print("Expected: Healthcare agent handles independently (minimal communication)\n")
    
    result4 = await system.process_query(
        "What are the symptoms of Type 2 diabetes?",
        "healthcare"
    )
    
    print(f"\n📋 Result Preview: {str(result4['result'])[:200]}...")
    
    # Final communication status
    print("\n" + "="*50)
    print("📊 Final Communication Summary")
    print("="*50)
    await system.display_communication_status()
    
    print("\n🎉 Communication Demo Completed!")
    print("\nKey Features Demonstrated:")
    print("✅ Automatic query classification and routing")
    print("✅ Cross-domain collaboration detection")
    print("✅ Inter-agent communication and messaging")
    print("✅ Intelligent task distribution")
    print("✅ Real-time communication monitoring")

async def demo_manual_communication():
    """Demonstrate manual communication between agents"""
    print("\n" + "="*60)
    print("🔧 MANUAL COMMUNICATION DEMO")
    print("="*60)
    print("This shows direct agent-to-agent communication capabilities.\n")
    
    system = MultiAgentSystem()
    await system.initialize()
    
    # Example of manual collaboration request
    print("🤝 Testing manual collaboration request...")
    print("Healthcare agent requesting AI expertise for 'machine learning in diagnostics'\n")
    
    # This would be done internally by agents, but we can demonstrate the concept
    from tools.communication_tools import communication_hub
    
    # Show current agent status
    print("📡 Current Agent Status:")
    agent_status = communication_hub.get_agent_status()
    for agent_id, status in agent_status.items():
        print(f"   • {agent_id}: {status['pending_messages']} messages, Capabilities: {', '.join(status['capabilities'][:2])}")
    
    print("\n🎯 Agents are ready for communication!")
    print("In a real scenario, agents would:")
    print("1. Detect when they need help from other domains")
    print("2. Send collaboration requests to appropriate agents")
    print("3. Receive and process responses from other agents")
    print("4. Integrate insights from multiple agents")
    print("5. Provide comprehensive answers to users")

def show_communication_architecture():
    """Show the communication architecture"""
    print("\n" + "="*60)
    print("🏗️ COMMUNICATION ARCHITECTURE")
    print("="*60)
    
    print("""
Communication Flow:
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│ Healthcare      │    │ AI Research     │    │ Coordinator     │
│ Agent           │    │ Agent           │    │ Agent           │
└─────────┬───────┘    └─────────┬───────┘    └─────────┬───────┘
          │                      │                      │
          └──────────────────────┼──────────────────────┘
                                 │
                    ┌─────────────▼─────────────┐
                    │   Communication Hub       │
                    │                           │
                    │ • Message Routing         │
                    │ • Collaboration Requests  │
                    │ • Broadcast Messages      │
                    │ • Conversation History    │
                    └───────────────────────────┘

Message Types:
• REQUEST: Direct requests between agents
• RESPONSE: Responses to requests
• COLLABORATION_REQUEST: Formal collaboration requests
• COLLABORATION_RESPONSE: Responses to collaboration requests
• BROADCAST: Messages sent to all agents
• NOTIFICATION: Status updates and notifications

Communication Features:
✅ Automatic collaboration detection
✅ Intelligent message routing
✅ Conversation history tracking
✅ Priority-based message handling
✅ Timeout management for responses
✅ Cross-agent knowledge sharing
    """)

async def main():
    """Main demo function"""
    print("🎭 Multi-Agent Communication System Demonstration")
    print("=" * 60)
    
    # Show architecture first
    show_communication_architecture()
    
    # Run communication demos
    await demo_communication()
    
    # Show manual communication capabilities
    await demo_manual_communication()
    
    print("\n" + "="*60)
    print("🎉 DEMONSTRATION COMPLETE!")
    print("="*60)
    print("The Multi-Agent Communication System is ready for use!")
    print("\nTo start using the system:")
    print("1. Run: python main.py")
    print("2. Try queries that span multiple domains")
    print("3. Watch agents communicate and collaborate")
    print("4. Use 'status' command to see communication activity")

if __name__ == "__main__":
    asyncio.run(main())
